// Умная система генерации ключевых слов для 100,000+ товаров

// Расширенный словарь материалов и их ключевых слов
export const MATERIAL_KEYWORDS = {
  // Бетон и растворы
  'бетон': [
    'бетон марки', 'бетонная смесь', 'товарный бетон', 'бетон класса',
    'тяжелый бетон', 'легкий бетон', 'монолитный бетон', 'сборный бетон'
  ],
  'раствор': [
    'строительный раствор', 'цементный раствор', 'кладочный раствор',
    'штукатурный раствор', 'монтажный раствор'
  ],
  'смесь': [
    'сухая смесь', 'строительная смесь', 'готовая смесь',
    'клеевая смесь', 'выравнивающая смесь'
  ],

  // Сыпучие материалы
  'щебень': [
    'щебень фракции', 'гранитный щебень', 'известняковый щебень',
    'щебень для дорог', 'щебень для бетона', 'декоративный щебень'
  ],
  'песок': [
    'строительный песок', 'речной песок', 'карьерный песок',
    'песок для бетона', 'кварцевый песок', 'мытый песок'
  ],
  'гравий': [
    'строительный гравий', 'гравий фракции', 'речной гравий',
    'морской гравий', 'горный гравий'
  ],
  'керамзит': [
    'керамзит фракции', 'керамзитовый гравий', 'утеплитель керамзит',
    'керамзитобетон', 'легкий заполнитель'
  ],

  // Кирпич и блоки
  'кирпич': [
    'строительный кирпич', 'облицовочный кирпич', 'керамический кирпич',
    'силикатный кирпич', 'огнеупорный кирпич', 'клинкерный кирпич'
  ],
  'блок': [
    'строительный блок', 'газобетонный блок', 'пеноблок',
    'керамзитобетонный блок', 'шлакоблок', 'арболитовый блок'
  ],
  'газобетон': [
    'газобетонные блоки', 'автоклавный газобетон', 'ячеистый бетон',
    'газосиликат', 'пористый бетон'
  ],

  // Вяжущие материалы
  'цемент': [
    'портландцемент', 'цемент марки', 'цемент м400', 'цемент м500',
    'быстротвердеющий цемент', 'белый цемент', 'сульфатостойкий цемент'
  ],
  'известь': [
    'строительная известь', 'гашеная известь', 'негашеная известь',
    'известковое тесто', 'известковое молоко'
  ],
  'гипс': [
    'строительный гипс', 'гипсовая смесь', 'алебастр',
    'формовочный гипс', 'медицинский гипс'
  ],

  // Металлопрокат
  'арматура': [
    'арматурная сталь', 'рифленая арматура', 'гладкая арматура',
    'композитная арматура', 'стеклопластиковая арматура'
  ],
  'профнастил': [
    'профилированный лист', 'кровельный профнастил', 'стеновой профнастил',
    'оцинкованный профнастил', 'окрашенный профнастил'
  ],
  'металлочерепица': [
    'кровельная металлочерепица', 'профилированная металлочерепица',
    'композитная металлочерепица', 'стальная металлочерепица'
  ],
  'труба': [
    'металлическая труба', 'стальная труба', 'профильная труба',
    'водопроводная труба', 'канализационная труба'
  ],

  // Кровельные материалы
  'черепица': [
    'кровельная черепица', 'керамическая черепица', 'цементно-песчаная черепица',
    'битумная черепица', 'композитная черепица'
  ],
  'шифер': [
    'асбестоцементный шифер', 'волновой шифер', 'плоский шифер',
    'еврошифер', 'ондулин'
  ],
  'рубероид': [
    'кровельный рубероид', 'подкладочный рубероид', 'наплавляемый рубероид',
    'самоклеящийся рубероид', 'армированный рубероид'
  ],

  // Изоляционные материалы
  'утеплитель': [
    'теплоизоляция', 'минеральная вата', 'пенополистирол',
    'экструдированный пенополистирол', 'пенополиуретан'
  ],
  'пенопласт': [
    'пенополистирол', 'теплоизоляция пенопласт', 'утеплитель пенопласт',
    'фасадный пенопласт', 'упаковочный пенопласт'
  ],
  'минвата': [
    'минеральная вата', 'базальтовая вата', 'каменная вата',
    'стекловата', 'шлаковата'
  ],

  // Отделочные материалы
  'плитка': [
    'керамическая плитка', 'напольная плитка', 'настенная плитка',
    'керамогранит', 'мозаика', 'клинкерная плитка'
  ],
  'ламинат': [
    'напольный ламинат', 'влагостойкий ламинат', 'ламинированное покрытие',
    'паркетная доска', 'инженерная доска'
  ],
  'линолеум': [
    'напольный линолеум', 'коммерческий линолеум', 'бытовой линолеум',
    'полукоммерческий линолеум', 'спортивный линолеум'
  ],

  // Лакокрасочные материалы
  'краска': [
    'строительная краска', 'фасадная краска', 'интерьерная краска',
    'водоэмульсионная краска', 'акриловая краска', 'масляная краска'
  ],
  'эмаль': [
    'алкидная эмаль', 'акриловая эмаль', 'защитная эмаль',
    'антикоррозийная эмаль', 'термостойкая эмаль'
  ],
  'грунтовка': [
    'строительная грунтовка', 'антикоррозийная грунтовка', 'адгезионная грунтовка',
    'глубокого проникновения', 'универсальная грунтовка'
  ],

  // Инструменты и крепеж
  'саморез': [
    'строительные саморезы', 'кровельные саморезы', 'саморезы по металлу',
    'саморезы по дереву', 'саморезы с пресс-шайбой'
  ],
  'гвоздь': [
    'строительные гвозди', 'кровельные гвозди', 'финишные гвозди',
    'толевые гвозди', 'дюбель-гвозди'
  ],
  'анкер': [
    'анкерные болты', 'химические анкеры', 'механические анкеры',
    'клиновые анкеры', 'распорные анкеры'
  ],

  // Сантехника и водоснабжение
  'фитинг': [
    'сантехнические фитинги', 'трубные фитинги', 'соединительные фитинги',
    'переходные фитинги', 'угловые фитинги'
  ],
  'вентиль': [
    'запорная арматура', 'шаровые краны', 'задвижки',
    'регулирующие вентили', 'обратные клапаны'
  ],

  // Электротехнические материалы
  'кабель': [
    'электрический кабель', 'силовой кабель', 'контрольный кабель',
    'телефонный кабель', 'интернет кабель'
  ],
  'провод': [
    'электрический провод', 'монтажный провод', 'установочный провод',
    'гибкий провод', 'одножильный провод'
  ]
};

// Функция для автоматической генерации ключевых слов
export function generateSmartKeywords(materialName) {
  const keywords = [];
  const name = materialName.toLowerCase();
  
  // Ищем совпадения в словаре и добавляем ключевые слова
  Object.entries(MATERIAL_KEYWORDS).forEach(([material, keywordList]) => {
    if (name.includes(material)) {
      keywords.push(...keywordList);
    }
  });
  
  // Добавляем технические характеристики из названия
  const technicalKeywords = extractTechnicalKeywords(name);
  keywords.push(...technicalKeywords);
  
  // Убираем дубликаты
  return [...new Set(keywords)];
}

// Извлечение технических характеристик из названия
function extractTechnicalKeywords(materialName) {
  const keywords = [];
  
  // ГОСТ и стандарты
  const gostMatch = materialName.match(/гост\s*(\d+[-\d]*)/gi);
  if (gostMatch) {
    gostMatch.forEach(gost => {
      keywords.push(gost.toLowerCase());
      keywords.push(`${gost.toLowerCase()} стандарт`);
    });
  }
  
  // Марки и классы
  const markaMatch = materialName.match(/м\d+/gi);
  if (markaMatch) {
    markaMatch.forEach(marka => {
      keywords.push(`${marka.toLowerCase()} марка`);
      keywords.push(`марка ${marka.toLowerCase()}`);
    });
  }
  
  // Классы бетона
  const classMatch = materialName.match(/в\d+[.,]?\d*/gi);
  if (classMatch) {
    classMatch.forEach(klass => {
      keywords.push(`класс ${klass.toLowerCase()}`);
      keywords.push(`${klass.toLowerCase()} класс`);
    });
  }
  
  // Фракции
  const fractionMatch = materialName.match(/\d+[-х]\d+/gi);
  if (fractionMatch) {
    fractionMatch.forEach(fraction => {
      keywords.push(`фракция ${fraction}`);
      keywords.push(`${fraction} фракция`);
    });
  }
  
  // Размеры
  const sizeMatch = materialName.match(/\d+х\d+х?\d*/gi);
  if (sizeMatch) {
    sizeMatch.forEach(size => {
      keywords.push(`размер ${size}`);
      keywords.push(`${size} размер`);
    });
  }
  
  return keywords;
}

// Генерация локальных ключевых слов
export function generateLocalKeywords(baseKeywords, cities = ["Астана", "Алматы", "Шымкент", "Караганда", "Актобе"]) {
  const localKeywords = [];
  
  cities.forEach(city => {
    localKeywords.push(
      `${baseKeywords} ${city}`,
      `купить ${baseKeywords} ${city}`,
      `доставка ${baseKeywords} ${city}`,
      `цена ${baseKeywords} ${city}`,
      `поставщики ${baseKeywords} ${city}`
    );
  });
  
  return localKeywords;
}

export default {
  MATERIAL_KEYWORDS,
  generateSmartKeywords,
  generateLocalKeywords
};
