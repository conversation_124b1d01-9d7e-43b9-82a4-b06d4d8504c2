import Layout from "../../../components/Layout";
import MyTenderProposalsClient from "./MyTenderProposalsClient";

export async function generateMetadata({ params }) {
  const { id } = params;

  return {
    title: `Ценовые предложения по тендеру ${id} | SADI Shop — строительный маркетплейс по всему Казахстану`,
    description: `Просмотр ценовых предложений по тендеру ${id}. Анализ поступивших предложений от поставщиков строительных материалов через SADI Shop — строительный маркетплейс, работающий по всему Казахстану.`,
    keywords: [
      // Основные термины
      "ценовые предложения",
      "тендер предложения",
      "анализ предложений",

      // Материалы (топ-запросы)
      "цемент предложения",
      "кирпич цены",
      "арматура стоимость",
      "бетон предложения",
      "песок цены",
      "щебень стоимость",
      "плитка предложения",
      "краска цены",

      // Города (приоритетные)
      "Алматы",
      "Астана",
      "Нур-Султан",
      "Шымкент",
      "Караганда",
      "Актобе",
      "Тараз",
      "Павлодар",
      "Усть-Каменогорск",
      "Семей",
      "Атырау",
      "Костанай",
      "Кызылорда",
      "Уральск",
      "Петропавловск",

      // Коммерческие запросы
      "поставщики материалов",
      "строительные компании",
      "оптовые цены",
      "дистрибьюторы",
      "производители стройматериалов",
      "импортеры",
      "торговые дома",
      "прайс листы",

      // Длинный хвост
      "сравнение ценовых предложений",
      "выбор поставщика стройматериалов",
      "строительный маркетплейс",
      "SADI Shop",
      "тендерные предложения Казахстан",
    ].join(", "),
    openGraph: {
      title: `Ценовые предложения по тендеру ${id} | SADI Shop`,
      description: `Просмотр ценовых предложений по тендеру ${id}. Анализ поступивших предложений от поставщиков строительных материалов.`,
      type: "website",
      locale: "ru_KZ",
    },
  };
}

export default function TenderProposalsPage({ params }) {
  return (
    <Layout>
      <MyTenderProposalsClient tenderId={params.id} />
    </Layout>
  );
}
