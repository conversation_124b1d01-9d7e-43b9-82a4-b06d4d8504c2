import ApiService from "../../services/api.service";
import ISR_CONFIG, { ISRUtils } from "../../config/isr";

// Базовый URL сайта
const BASE_URL = "https://shop.sadi.kz";

// Генерация sitemap для отдельных товаров (только популярные для экономии invocations)
export async function GET() {
  try {
    // Увеличиваем количество товаров для лучшего SEO
    const maxProducts = 1000; // 1000 самых важных товаров
    const productsData = await ApiService.getProductsWithPagination(
      1,
      maxProducts
    );
    const products = productsData.data || productsData.products || [];

    ISRUtils.logISROperation("sitemap-products", {
      requested: maxProducts,
      generated: products.length,
    });

    // Создаем XML для товаров
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Добавляем товары с оптимизированными приоритетами
    products.forEach((product, index) => {
      if (product.MaterialId) {
        // Используем ISR конфигурацию для определения приоритетов
        const priority = ISR_CONFIG.getProductPriority(
          product.MaterialId,
          index
        );
        const changefreq = ISR_CONFIG.getChangeFrequency("product", index);

        xml += `
  <url>
    <loc>${BASE_URL}/product/${product.MaterialId}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`;
      }
    });

    xml += `
</urlset>`;

    return new Response(xml, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600", // Кэшируем на 1 час
      },
    });
  } catch (error) {
    console.error("Ошибка при генерации sitemap-products:", error);

    // Возвращаем пустой sitemap в случае ошибки
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

    return new Response(xml, {
      headers: {
        "Content-Type": "application/xml",
      },
    });
  }
}
