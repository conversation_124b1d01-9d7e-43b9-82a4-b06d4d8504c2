"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import Layout from "../../components/Layout";
import OrderModal from "../../components/OrderModal";
import { useCart } from "../../context/CartContext";
import { useAveragePricesContext } from "../../context/AveragePricesContext";
import { useAuth } from "../../context/AuthContext";
import API_CONFIG from "../../config/api";

// Функция для получения текущей даты в формате YYYY.MM.DD
const getCurrentDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  return `${year}.${month}.${day}`;
};

const CartContainer = styled.div`
  padding: 24px;
  flex-grow: 1;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;
CartContainer.displayName = "CartContainer";

const Title = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    font-size: 22px;
    margin-bottom: 16px;
  }
`;
Title.displayName = "Title";

const Breadcrumbs = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  font-size: 14px;
  color: #666;

  a {
    color: #666;
    text-decoration: none;

    &:hover {
      color: #0066cc;
    }
  }

  svg {
    margin: 0 8px;
    width: 16px;
    height: 16px;
  }

  @media (max-width: 768px) {
    margin-bottom: 16px;
  }
`;
Breadcrumbs.displayName = "Breadcrumbs";

const BackButton = styled.button`
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;

  &:hover {
    color: #0066cc;
  }

  svg {
    margin-right: 4px;
  }
`;
BackButton.displayName = "BackButton";

const CartTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    display: none;
  }

  th {
    background-color: #f8f9fa;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    border-bottom: 1px solid #e0e0e0;
  }

  td {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: middle;
  }

  tr:hover {
    background-color: #f8f9fa;
  }
`;
CartTable.displayName = "CartTable";

const ProductName = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 300px;

  img {
    width: 50px;
    height: 50px;
    object-fit: contain;
    border-radius: 4px;
  }

  span {
    font-size: 14px;
    line-height: 1.4;
  }
`;
ProductName.displayName = "ProductName";

const PriceCell = styled.div`
  font-size: 14px;
  color: ${(props) => (props.available ? "#333" : "#999")};
`;
PriceCell.displayName = "PriceCell";

const QuantityInput = styled.input`
  width: 80px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
QuantityInput.displayName = "QuantityInput";

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 8px;

  &:hover {
    color: #ff4444;
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;
RemoveButton.displayName = "RemoveButton";

const MobileCartContainer = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
`;
MobileCartContainer.displayName = "MobileCartContainer";

const MobileCartItem = styled.div`
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
`;
MobileCartItem.displayName = "MobileCartItem";

const MobileProductHeader = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
`;
MobileProductHeader.displayName = "MobileProductHeader";

const MobileProductInfo = styled.div`
  flex: 1;
`;
MobileProductInfo.displayName = "MobileProductInfo";

const MobileProductName = styled.h3`
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
`;
MobileProductName.displayName = "MobileProductName";

const MobileRemoveButton = styled.button`
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;

  &:hover {
    color: #ff4444;
  }
`;
MobileRemoveButton.displayName = "MobileRemoveButton";

const MobilePriceRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;

  span:first-child {
    color: #666;
  }

  span:last-child {
    font-weight: 500;
    color: ${(props) => (props.available ? "#333" : "#999")};
  }
`;
MobilePriceRow.displayName = "MobilePriceRow";

const MobileQuantityRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
`;
MobileQuantityRow.displayName = "MobileQuantityRow";

const ButtonsContainer = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
  }
`;
ButtonsContainer.displayName = "ButtonsContainer";

const PrimaryButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
PrimaryButton.displayName = "PrimaryButton";

const SecondaryButton = styled.button`
  background-color: white;
  color: #333;
  border: 2px solid #d6dce1;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    border-color: #0066cc;
  }
`;
SecondaryButton.displayName = "SecondaryButton";

// Стили для уведомлений
const ErrorNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
ErrorNotification.displayName = "ErrorNotification";

const ErrorNotificationText = styled.span`
  flex: 1;
`;
ErrorNotificationText.displayName = "ErrorNotificationText";

const ErrorNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
ErrorNotificationClose.displayName = "ErrorNotificationClose";

const CartPage = () => {
  const router = useRouter();
  const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);
  const [materialsData, setMaterialsData] = useState({});
  const { cartItems, removeFromCart, updateQuantity, updateQuantityTemp } =
    useCart();
  const { updatePricesForProducts, getPriceByMaterialId } =
    useAveragePricesContext();
  const { user, isAuthenticated } = useAuth();

  // Состояния для уведомлений
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");

  // Обновляем средние цены и фотографии для товаров в корзине
  useEffect(() => {
    if (cartItems && cartItems.length > 0) {
      // Извлекаем MaterialId из товаров в корзине
      const cartProducts = cartItems.map((item) => ({
        MaterialId: parseInt(item.id.toString().split("-")[0]), // Извлекаем MaterialId из id товара
      }));
      // Теперь updatePricesForProducts обновляет и цены, и фотографии
      updatePricesForProducts(cartProducts);
    }
  }, [cartItems, updatePricesForProducts]);

  // Загружаем данные о материалах для получения единиц измерения
  useEffect(() => {
    if (cartItems && cartItems.length > 0) {
      const materialIds = cartItems.map((item) =>
        parseInt(item.id.toString().split("-")[0])
      );

      fetchMaterialsData(materialIds).then((data) => {
        const materialsMap = {};
        data.forEach((material) => {
          materialsMap[material.MaterialId] = material;
        });
        setMaterialsData(materialsMap);
      });
    }
  }, [cartItems]);

  // Функция для получения актуальных цен товара
  const getItemPrices = (item) => {
    // Если товар от конкретного поставщика, используем его цены
    if (item.isFromSpecificSupplier) {
      return {
        retailPrice: item.originalRetailPrice || item.retailPrice,
        wholesalePrice: item.originalWholesalePrice || item.wholesalePrice,
      };
    }

    // Для обычных товаров используем средние цены или оригинальные
    const materialId = parseInt(item.id.toString().split("-")[0]);
    const averagePrice = getPriceByMaterialId(materialId);

    return {
      retailPrice: averagePrice?.AvgRetailPrice || item.retailPrice,
      wholesalePrice: averagePrice?.AvgTradePrice || item.wholesalePrice,
    };
  };

  // Функция для получения единицы измерения товара
  const getItemUnit = (item) => {
    const materialId = parseInt(item.id.toString().split("-")[0]);
    const materialData = materialsData[materialId];
    return materialData?.UnitId || "";
  };

  const handleBack = () => {
    router.push("/");
  };

  const handleRemoveItem = (productId) => {
    removeFromCart(productId);
  };

  const handleQuantityChange = (productId, value) => {
    // Разрешаем любое значение во время ввода, включая пустую строку
    const quantity = value === "" ? "" : parseInt(value, 10);

    // Используем временное обновление для возможности редактирования
    updateQuantityTemp(productId, quantity);
  };

  const handleQuantityBlur = (productId, value) => {
    // При потере фокуса проверяем и корректируем значение
    let quantity = parseInt(value, 10);

    if (isNaN(quantity) || quantity < 1) {
      quantity = 1;
    }

    updateQuantity(productId, quantity);
  };

  // Функция для расчета общей стоимости с актуальными ценами
  const getCartTotalWithActualPrices = () => {
    return cartItems.reduce((total, item) => {
      const prices = getItemPrices(item);
      const retailPrice = prices.retailPrice;

      // Если цена не указана, не добавляем к общей сумме
      if (!retailPrice) return total;

      // Если количество пустое или не число, считаем как 0
      const quantity =
        typeof item.quantity === "number"
          ? item.quantity
          : parseInt(item.quantity, 10) || 0;

      return total + retailPrice * quantity;
    }, 0);
  };

  // Функции для управления уведомлениями
  const showError = (message) => {
    setNotificationMessage(message);
    setShowErrorNotification(true);
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 3000);
  };

  const handleCloseErrorNotification = () => {
    setShowErrorNotification(false);
  };

  // Функция для создания тендера из корзины
  const handleCreateTender = async () => {
    // Проверяем авторизацию
    if (!isAuthenticated) {
      router.push("/auth?from=tender");
      return;
    }

    // Проверяем companyId
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000" ||
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      router.push("/auth?from=company");
      return;
    }

    if (cartItems.length === 0) {
      showError("Корзина пуста. Добавьте товары для создания тендера");
      return;
    }

    try {
      // Получаем MaterialId из товаров в корзине
      const materialIds = cartItems.map((item) => {
        // Извлекаем MaterialId из id товара (может быть как простой id, так и составной с AdvertId)
        return parseInt(item.id.toString().split("-")[0]);
      });

      console.log("Создание тендера из корзины для материалов:", materialIds);

      // Получаем полную информацию о материалах из API
      const materialsData = await fetchMaterialsData(materialIds);

      // Преобразуем товары корзины в формат для tender-form
      const tenderProducts = cartItems.map((cartItem) => {
        const materialId = parseInt(cartItem.id.toString().split("-")[0]);
        const materialData = materialsData.find(
          (m) => m.MaterialId === materialId
        );

        return {
          MaterialId: materialId,
          MaterialName: cartItem.title,
          MaterialCode: materialData?.Code || "",
          Code: materialData?.Code || "",
          UnitId: materialData?.UnitId || "",
          // Добавляем дополнительные поля если они есть в API
          ...materialData,
        };
      });

      // Сохраняем товары в localStorage для tender-form
      localStorage.setItem(
        "selectedTenderProducts",
        JSON.stringify(tenderProducts)
      );

      // Получаем рекомендуемые цены
      await fetchAveragePrices(materialIds);

      // Переходим на страницу создания тендера
      router.push("/tender-form");
    } catch (error) {
      console.error("Ошибка при создании тендера из корзины:", error);
      showError(`Ошибка при создании тендера: ${error.message}`);
    }
  };

  // Функция для получения полной информации о материалах из API
  const fetchMaterialsData = async (materialIds) => {
    try {
      const promises = materialIds.map(async (materialId) => {
        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/Materials/${materialId}`
        );
        if (response.ok) {
          return await response.json();
        }
        return null;
      });

      const results = await Promise.all(promises);
      return results.filter((result) => result !== null);
    } catch (error) {
      console.error("Ошибка при получении данных материалов:", error);
      return [];
    }
  };

  // Функция для получения рекомендуемых цен
  const fetchAveragePrices = async (materialIds) => {
    try {
      // Формируем URL с параметрами materialIds
      const params = new URLSearchParams();
      materialIds.forEach((id) => {
        params.append("materialIds", id);
      });
      params.append("fromDate", "2000.01.01");
      params.append("tillDate", getCurrentDate());

      const url = `${
        API_CONFIG.BASE_URL
      }/api/Adverts/AveragePrices?${params.toString()}`;

      console.log(
        `Запрос рекомендуемых цен для ${materialIds.length} материалов из корзины: ${url}`
      );

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const averagePrices = await response.json();
        console.log("Получены рекомендуемые цены для корзины:", averagePrices);

        // Сохраняем рекомендуемые цены в localStorage
        localStorage.setItem(
          "tenderAveragePrices",
          JSON.stringify(averagePrices)
        );
      } else {
        console.warn(
          "Не удалось получить рекомендуемые цены:",
          response.status
        );
        // Очищаем старые данные о ценах
        localStorage.removeItem("tenderAveragePrices");
      }
    } catch (error) {
      console.error("Ошибка при получении рекомендуемых цен:", error);
      // Очищаем старые данные о ценах
      localStorage.removeItem("tenderAveragePrices");
    }
  };

  return (
    <>
      {/* Уведомления */}
      {showErrorNotification && (
        <ErrorNotification>
          <ErrorNotificationText>{notificationMessage}</ErrorNotificationText>
          <ErrorNotificationClose onClick={handleCloseErrorNotification}>
            ×
          </ErrorNotificationClose>
        </ErrorNotification>
      )}

      <Layout>
        <CartContainer>
          <Breadcrumbs>
            <BackButton onClick={handleBack}>
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10 12L6 8L10 4"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Назад
            </BackButton>
            <Link href="/">Прайс листы</Link>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 12L10 8L6 4"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span>Корзина</span>
          </Breadcrumbs>

          <Title>Корзина</Title>

          {cartItems.length === 0 ? (
            <div
              style={{ textAlign: "center", padding: "40px 0", color: "#666" }}
            >
              <p>Ваша корзина пуста</p>
              <ButtonsContainer style={{ justifyContent: "center" }}>
                <SecondaryButton onClick={() => router.push("/")}>
                  Перейти к каталогу
                </SecondaryButton>
              </ButtonsContainer>
            </div>
          ) : (
            <>
              {/* Десктопная версия - таблица */}
              <CartTable>
                <thead>
                  <tr>
                    <th>НАИМЕНОВАНИЕ ТОВАРА</th>
                    <th>РОЗНИЦА</th>
                    <th>ОПТ</th>
                    <th>НЕОБХОДИМОЕ КОЛ.ВО</th>
                    <th>ИТОГОВАЯ ЦЕНА</th>
                    <th>Единица измерения</th>
                    <th>Действие</th>
                  </tr>
                </thead>
                <tbody>
                  {cartItems.map((item) => {
                    const prices = getItemPrices(item);
                    const quantity =
                      typeof item.quantity === "number"
                        ? item.quantity
                        : parseInt(item.quantity, 10) || 0;
                    const totalPrice = prices.retailPrice
                      ? prices.retailPrice * quantity
                      : 0;

                    return (
                      <tr key={item.id}>
                        <td>
                          <ProductName>
                            <img
                              src={item.image || "/images/placeholder.png"}
                              alt={item.title}
                            />
                            <span>{item.title}</span>
                          </ProductName>
                        </td>
                        <td>
                          <PriceCell available={!!prices.retailPrice}>
                            {prices.retailPrice
                              ? `${prices.retailPrice.toLocaleString()} ₸`
                              : "По запросу"}
                          </PriceCell>
                        </td>
                        <td>
                          <PriceCell available={!!prices.wholesalePrice}>
                            {prices.wholesalePrice
                              ? `${prices.wholesalePrice.toLocaleString()} ₸`
                              : "По запросу"}
                          </PriceCell>
                        </td>
                        <td>
                          <QuantityInput
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) =>
                              handleQuantityChange(item.id, e.target.value)
                            }
                            onBlur={(e) =>
                              handleQuantityBlur(item.id, e.target.value)
                            }
                          />
                        </td>
                        <td>
                          <PriceCell available={!!prices.retailPrice}>
                            {totalPrice > 0
                              ? `${totalPrice.toLocaleString()} ₸`
                              : "По запросу"}
                          </PriceCell>
                        </td>
                        <td>{getItemUnit(item)}</td>
                        <td>
                          <RemoveButton
                            onClick={() => handleRemoveItem(item.id)}
                          >
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M2 4H3.33333H14"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M12.6667 4V13.3333C12.6667 14 12 14.6667 11.3333 14.6667H4.66667C4 14.6667 3.33333 14 3.33333 13.3333V4"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </RemoveButton>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </CartTable>

              {/* Мобильная версия - карточки */}
              <MobileCartContainer>
                {cartItems.map((item) => {
                  const prices = getItemPrices(item);
                  const quantity =
                    typeof item.quantity === "number"
                      ? item.quantity
                      : parseInt(item.quantity, 10) || 0;
                  const totalPrice = prices.retailPrice
                    ? prices.retailPrice * quantity
                    : 0;

                  return (
                    <MobileCartItem key={item.id}>
                      <MobileProductHeader>
                        <img
                          src={item.image || "/images/placeholder.png"}
                          alt={item.title}
                          style={{
                            width: "60px",
                            height: "60px",
                            objectFit: "contain",
                            borderRadius: "4px",
                          }}
                        />
                        <MobileProductInfo>
                          <MobileProductName>{item.title}</MobileProductName>
                        </MobileProductInfo>
                        <MobileRemoveButton
                          onClick={() => handleRemoveItem(item.id)}
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M2 4H3.33333H14"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M12.6667 4V13.3333C12.6667 14 12 14.6667 11.3333 14.6667H4.66667C4 14.6667 3.33333 14 3.33333 13.3333V4"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </MobileRemoveButton>
                      </MobileProductHeader>

                      <MobilePriceRow available={!!prices.retailPrice}>
                        <span>Розница:</span>
                        <span>
                          {prices.retailPrice
                            ? `${prices.retailPrice.toLocaleString()} ₸`
                            : "По запросу"}
                        </span>
                      </MobilePriceRow>

                      <MobilePriceRow available={!!prices.wholesalePrice}>
                        <span>Опт:</span>
                        <span>
                          {prices.wholesalePrice
                            ? `${prices.wholesalePrice.toLocaleString()} ₸`
                            : "По запросу"}
                        </span>
                      </MobilePriceRow>

                      <MobilePriceRow>
                        <span>Ед. изм.:</span>
                        <span>{getItemUnit(item)}</span>
                      </MobilePriceRow>

                      <MobileQuantityRow>
                        <span style={{ color: "#666" }}>Количество:</span>
                        <QuantityInput
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) =>
                            handleQuantityChange(item.id, e.target.value)
                          }
                          onBlur={(e) =>
                            handleQuantityBlur(item.id, e.target.value)
                          }
                        />
                      </MobileQuantityRow>

                      <MobilePriceRow
                        available={!!prices.retailPrice}
                        style={{ marginTop: "16px", fontWeight: "600" }}
                      >
                        <span>Итого:</span>
                        <span>
                          {totalPrice > 0
                            ? `${totalPrice.toLocaleString()} ₸`
                            : "По запросу"}
                        </span>
                      </MobilePriceRow>
                    </MobileCartItem>
                  );
                })}
              </MobileCartContainer>

              <div
                style={{
                  textAlign: "right",
                  margin: "16px 0",
                  fontWeight: "bold",
                  fontSize: "18px",
                }}
              >
                Итого:{" "}
                {getCartTotalWithActualPrices() > 0
                  ? getCartTotalWithActualPrices().toLocaleString()
                  : 0}{" "}
                ₸
              </div>

              <ButtonsContainer>
                <PrimaryButton onClick={() => setIsOrderModalOpen(true)}>
                  Оформить заказ
                </PrimaryButton>
                <SecondaryButton onClick={handleCreateTender}>
                  Создать тендер
                </SecondaryButton>
                <SecondaryButton onClick={() => router.push("/")}>
                  Добавить еще позиции
                </SecondaryButton>
              </ButtonsContainer>
            </>
          )}
        </CartContainer>

        <OrderModal
          isOpen={isOrderModalOpen}
          onClose={() => setIsOrderModalOpen(false)}
          cartItems={cartItems}
          getItemPrices={getItemPrices}
        />
      </Layout>
    </>
  );
};

export default CartPage;
