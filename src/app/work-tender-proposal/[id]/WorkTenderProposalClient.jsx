"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import API_CONFIG from "../../../config/api";
import { useAuth } from "../../../context/AuthContext";
import authService from "../../../services/auth.service";

const TenderProposalContainer = styled.div`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderProposalContainer.displayName = "TenderProposalContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const BackButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "<PERSON><PERSON>",
    sans-serif;
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  border: none;
  margin-left: 60px;

  &:hover {
    background-color: #f8f9fa;
  }

  @media (max-width: 768px) {
    margin-left: -20px;
  }
`;
BackButton.displayName = "BackButton";

const Title = styled.h1`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -0.5px;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 28px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 20px;
  font-weight: 700;
  line-height: 130%;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const TenderInfoCard = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
`;
TenderInfoCard.displayName = "TenderInfoCard";

const TenderTitle = styled.h3`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 20px;
  font-weight: 700;
  line-height: 130%;
  color: #333;
  margin-bottom: 12px;
`;
TenderTitle.displayName = "TenderTitle";

const TenderDetail = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;

  &:last-child {
    margin-bottom: 0;
  }
`;
TenderDetail.displayName = "TenderDetail";

const Label = styled.div`
  font-size: 14px;
  color: #969ea7;
  margin-bottom: 10px;

  @media (max-width: 768px) {
    font-size: 17px;
  }
`;
Label.displayName = "Label";

const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  transition: all 0.3s ease;
  margin-bottom: 8px;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  font-size: 14px;
  color: #333;
  font-weight: 500;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  font-size: 12px;
  color: #6c757d;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const SubmitButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
  margin-top: 24px;

  &:hover {
    background-color: #0055b3;
  }

  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }
`;
SubmitButton.displayName = "SubmitButton";

const FileUploadSection = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
`;
FileUploadSection.displayName = "FileUploadSection";

const WorkTenderProposalClient = ({ tenderId }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [tenderInfo, setTenderInfo] = useState(null);
  const [tenderDetails, setTenderDetails] = useState(null);
  const [tenderPhotos, setTenderPhotos] = useState([]);
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Функция для форматирования размера файла
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Функция для форматирования даты
  const formatDate = (dateString) => {
    if (!dateString) return "Не указано";
    const date = new Date(dateString);
    return date.toLocaleDateString("ru-RU", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Функция для загрузки информации о тендере
  const fetchTenderInfo = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Загружаем основную информацию о тендере
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables/${tenderId}`
      );

      if (!response.ok) {
        throw new Error(`Ошибка загрузки тендера: ${response.status}`);
      }

      const data = await response.json();
      setTenderInfo(data);
      setTenderDetails(data);

      // Загружаем фотографии тендера
      await fetchTenderPhotos();
    } catch (err) {
      console.error("Ошибка при загрузке тендера:", err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Функция для загрузки фотографий тендера
  const fetchTenderPhotos = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTableFiles?purchReqId=${tenderId}`
      );
      if (response.ok) {
        const data = await response.json();
        setTenderPhotos(Array.isArray(data) ? data : []);
      } else {
        console.warn(`Не удалось загрузить фотографии тендера ${tenderId}`);
        setTenderPhotos([]);
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий тендера:", error);
      setTenderPhotos([]);
    }
  };

  // Функция для загрузки файлов предложения
  const uploadProposalFiles = async (purchReqId, attachedFiles) => {
    if (!attachedFiles || attachedFiles.length === 0) {
      console.log("📎 Нет файлов для загрузки");
      return;
    }

    console.log(
      `📎 Загружаем ${attachedFiles.length} файлов для тендера ${purchReqId}...`
    );

    try {
      const uploadPromises = attachedFiles.map(async (fileObj, index) => {
        const formData = new FormData();
        formData.append("file", fileObj.file, fileObj.name);

        console.log(
          `📤 Загружаем файл ${index + 1}/${attachedFiles.length}: ${
            fileObj.name
          }`
        );

        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/PurchReqTableFiles?purchReqId=${purchReqId}`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!response.ok) {
          throw new Error(
            `Ошибка загрузки файла ${fileObj.name}: ${response.status}`
          );
        }

        const result = await response.json();
        console.log(`✅ Файл ${fileObj.name} успешно загружен:`, result);
        return result;
      });

      await Promise.all(uploadPromises);
      console.log("✅ Все файлы успешно загружены");
    } catch (error) {
      console.error("❌ Ошибка при загрузке файлов:", error);
      throw error;
    }
  };

  // Функция для обработки выбора файлов
  const handleFileSelect = (event) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);

    // Проверяем размер файлов (максимум 10MB на файл)
    const maxSize = 10 * 1024 * 1024; // 10MB
    const oversizedFiles = fileArray.filter((file) => file.size > maxSize);

    if (oversizedFiles.length > 0) {
      alert(
        `Файлы слишком большие. Максимальный размер: 10MB\n${oversizedFiles
          .map((f) => f.name)
          .join("\n")}`
      );
      return;
    }

    // Создаем объекты файлов с дополнительной информацией
    const newFiles = fileArray.map((file) => ({
      file: file,
      name: file.name,
      size: file.size,
      type: file.type,
      id: Date.now() + Math.random(), // уникальный ID
    }));

    setAttachedFiles((prev) => [...prev, ...newFiles]);

    // Очищаем input для возможности повторного выбора того же файла
    event.target.value = "";
  };

  // Функция для удаления файла
  const handleRemoveFile = (fileId) => {
    setAttachedFiles((prev) => prev.filter((file) => file.id !== fileId));
  };

  // Функция для отправки предложения
  const handleSubmit = async () => {
    if (!user) {
      alert("Необходимо авторизоваться для подачи предложения");
      return;
    }

    if (!tenderInfo) {
      alert("Информация о тендере не загружена");
      return;
    }

    setIsSubmitting(true);

    try {
      // Загружаем файлы, если они есть
      if (attachedFiles && attachedFiles.length > 0) {
        console.log(
          `📎 Загружаем ${attachedFiles.length} файлов для тендера ${tenderId}...`
        );
        await uploadProposalFiles(tenderId, attachedFiles);
      }

      // Показываем уведомление об успехе
      setShowSuccessNotification(true);
      setTimeout(() => {
        setShowSuccessNotification(false);
        // Переходим обратно к списку тендеров
        router.push("/find-work-tender");
      }, 2000);
    } catch (error) {
      console.error("❌ Ошибка при отправке предложения:", error);
      setShowErrorNotification(true);
      setTimeout(() => setShowErrorNotification(false), 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Функция для возврата назад
  const handleBack = () => {
    router.push("/find-work-tender");
  };

  // Функция для скачивания файла
  const handleDownloadFile = (fileUrl, fileName) => {
    if (!fileUrl) {
      console.warn("URL файла не найден");
      return;
    }

    try {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName || "file";
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Ошибка при скачивании файла:", error);
      // Fallback - открываем в новой вкладке
      window.open(fileUrl, "_blank");
    }
  };

  // Функция для определения типа файла
  const getFileType = (fileName) => {
    if (!fileName) return "unknown";
    const extension = fileName.split(".").pop()?.toLowerCase();
    const imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
    const documentExtensions = ["pdf", "doc", "docx", "xls", "xlsx", "txt"];
    const archiveExtensions = ["zip", "rar", "7z"];

    if (imageExtensions.includes(extension)) return "image";
    if (documentExtensions.includes(extension)) return "document";
    if (archiveExtensions.includes(extension)) return "archive";
    return "unknown";
  };

  // Загрузка данных при монтировании компонента
  useEffect(() => {
    if (tenderId) {
      fetchTenderInfo();
    }
  }, [tenderId]);

  // Проверка авторизации
  useEffect(() => {
    const checkAuth = async () => {
      const token = authService.getToken();
      if (!token) {
        router.push("/auth?from=tender");
        return;
      }
    };

    checkAuth();
  }, [router]);

  if (isLoading) {
    return (
      <TenderProposalContainer>
        <ContentContainer>
          <div style={{ textAlign: "center", padding: "40px" }}>
            <div style={{ fontSize: "18px", color: "#666" }}>
              Загрузка информации о тендере...
            </div>
          </div>
        </ContentContainer>
      </TenderProposalContainer>
    );
  }

  if (error) {
    return (
      <TenderProposalContainer>
        <ContentContainer>
          <div style={{ textAlign: "center", padding: "40px" }}>
            <div
              style={{
                fontSize: "18px",
                color: "#dc3545",
                marginBottom: "16px",
              }}
            >
              Ошибка загрузки: {error}
            </div>
            <button
              onClick={() => router.push("/find-work-tender")}
              style={{
                padding: "8px 16px",
                backgroundColor: "#0066cc",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              Вернуться к поиску тендеров
            </button>
          </div>
        </ContentContainer>
      </TenderProposalContainer>
    );
  }

  if (!tenderInfo) {
    return (
      <TenderProposalContainer>
        <ContentContainer>
          <div style={{ textAlign: "center", padding: "40px" }}>
            <div style={{ fontSize: "18px", color: "#666" }}>
              Тендер не найден
            </div>
          </div>
        </ContentContainer>
      </TenderProposalContainer>
    );
  }

  return (
    <>
      {/* Уведомления */}
      {showSuccessNotification && (
        <div
          style={{
            position: "fixed",
            top: "20px",
            right: "20px",
            backgroundColor: "#28a745",
            color: "white",
            padding: "12px 20px",
            borderRadius: "4px",
            zIndex: 1000,
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          ✅ Предложение успешно отправлено!
        </div>
      )}

      {showErrorNotification && (
        <div
          style={{
            position: "fixed",
            top: "20px",
            right: "20px",
            backgroundColor: "#dc3545",
            color: "white",
            padding: "12px 20px",
            borderRadius: "4px",
            zIndex: 1000,
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          ❌ Ошибка при отправке предложения
        </div>
      )}

      <TenderProposalContainer>
        <BackButton onClick={handleBack}>
          <img
            src="/icons/arrow_back_24px.svg"
            alt="Назад"
            style={{ width: "12px", height: "12px" }}
          />
          Назад к поиску тендеров
        </BackButton>

        <ContentContainer>
          <Title>Подача предложения по тендеру на работы</Title>
          <Text>
            Ознакомьтесь с требованиями тендера и приложите необходимые
            документы
          </Text>

          {/* Информация о тендере */}
          <TenderInfoCard>
            <TenderTitle>{tenderInfo.PurchReqName}</TenderTitle>
            <TenderDetail>
              <strong>Дата окончания:</strong>{" "}
              {formatDate(tenderInfo.PurchEndDate)}
            </TenderDetail>
            {tenderInfo.DeliveryAddress && (
              <TenderDetail>
                <strong>Адрес выполнения работ:</strong>{" "}
                {tenderInfo.DeliveryAddress}
              </TenderDetail>
            )}
            {tenderInfo.Description && (
              <TenderDetail>
                <strong>Описание:</strong> {tenderInfo.Description}
              </TenderDetail>
            )}
            {tenderInfo.PurchBudget && (
              <TenderDetail>
                <strong>Бюджет:</strong>{" "}
                {tenderInfo.PurchBudget.toLocaleString()} ₸
              </TenderDetail>
            )}
          </TenderInfoCard>

          {/* Фотографии тендера */}
          {tenderPhotos && tenderPhotos.length > 0 && (
            <TenderInfoCard style={{ marginBottom: "24px" }}>
              <TenderTitle>Прикрепленные файлы</TenderTitle>
              <div
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "12px",
                  marginTop: "12px",
                }}
              >
                {tenderPhotos.map((photo, index) => {
                  const fileType = getFileType(photo.FileName);
                  const isImage = fileType === "image";

                  return (
                    <div
                      key={photo.PurchReqTableFotoId}
                      style={{ position: "relative", cursor: "pointer" }}
                      onClick={() =>
                        handleDownloadFile(photo.FileUrl, photo.FileName)
                      }
                    >
                      {isImage ? (
                        <img
                          src={photo.FileUrl}
                          alt={photo.FileName}
                          style={{
                            width: "100px",
                            height: "100px",
                            objectFit: "cover",
                            borderRadius: "8px",
                            border: "1px solid #e0e0e0",
                          }}
                        />
                      ) : (
                        <div
                          style={{
                            width: "100px",
                            height: "100px",
                            backgroundColor: "#f8f9fa",
                            border: "1px solid #e0e0e0",
                            borderRadius: "8px",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            padding: "8px",
                            textAlign: "center",
                          }}
                        >
                          <div
                            style={{ fontSize: "24px", marginBottom: "4px" }}
                          >
                            📄
                          </div>
                          <div
                            style={{
                              fontSize: "10px",
                              color: "#666",
                              wordBreak: "break-all",
                              lineHeight: "1.2",
                            }}
                          >
                            {photo.FileName}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </TenderInfoCard>
          )}

          {/* Секция прикрепления файлов */}
          <SectionTitle>Прикрепление файлов</SectionTitle>
          <FileUploadSection>
            <Label>
              Прикрепите документы, подтверждающие вашу квалификацию и опыт
              выполнения подобных работ
            </Label>

            <UploadButton
              onClick={() => document.getElementById("file-input").click()}
              type="button"
            >
              <img src="/icons/Upload.svg" alt="Загрузить" />
              <UploadText>Прикрепить файл</UploadText>
            </UploadButton>

            <HiddenFileInput
              id="file-input"
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.zip,.rar"
              onChange={handleFileSelect}
            />

            {/* Список прикрепленных файлов */}
            {attachedFiles && attachedFiles.length > 0 && (
              <AttachedFilesList>
                {attachedFiles.map((fileObj) => (
                  <AttachedFileItem key={fileObj.id}>
                    <FileInfo>
                      <FileName>{fileObj.name}</FileName>
                      <FileSize>{formatFileSize(fileObj.size)}</FileSize>
                    </FileInfo>
                    <RemoveFileButton
                      onClick={() => handleRemoveFile(fileObj.id)}
                      title="Удалить файл"
                    >
                      ×
                    </RemoveFileButton>
                  </AttachedFileItem>
                ))}
              </AttachedFilesList>
            )}

            <Label
              style={{ marginTop: "12px", fontSize: "12px", color: "#6c757d" }}
            >
              Сертификаты, лицензии, портфолио выполненных работ, другая
              полезная информация
            </Label>
          </FileUploadSection>

          {/* Кнопка отправки предложения */}
          <div style={{ textAlign: "center", marginTop: "32px" }}>
            {/* <SubmitButton onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Отправка..." : "ОТПРАВИТЬ ПРЕДЛОЖЕНИЕ"}
              {!isSubmitting && (
                <img
                  src="/icons/CheckCreateTender.svg"
                  width={"15"}
                  height={"15"}
                  alt="Отправить"
                />
              )}
            </SubmitButton> */}
          </div>
        </ContentContainer>
      </TenderProposalContainer>
    </>
  );
};

export default WorkTenderProposalClient;
