import Layout from "../../../components/Layout";
import WorkTenderProposalClient from "./WorkTenderProposalClient";

export const metadata = {
  title:
    "Предложение по тендеру на работы | SADI Shop — строительный маркетплейс по всему Казахстану",
  description:
    "Подайте предложение по тендеру на строительные работы в SADI Shop — строительном маркетплейсе, работающем по всему Казахстану",
  keywords: [
    // Основные термины
    "предложение по тендеру",
    "подача предложения",
    "тендер на работы",

    // Виды работ (топ-запросы)
    "строительные работы предложение",
    "ремонтные работы тендер",
    "отделочные работы",
    "кровельные работы",
    "электромонтажные работы",
    "сантехнические работы",

    // Города (приоритетные)
    "Алматы",
    "Астана",
    "Нур-Султан",
    "Шымкент",
    "Караганда",
    "Актобе",
    "Тараз",
    "Павлодар",
    "Усть-Каменогорск",
    "Семей",
    "Атырау",
    "Костанай",
    "Кызылорда",
    "Уральск",
    "Петропавловск",

    // Коммерческие запросы
    "подрядчик",
    "строительная компания",
    "исполнитель работ",
    "субподрядчик",
    "строительные услуги",
    "ремонтные услуги",
    "монтажные работы",

    // Длинный хвост
    "как подать предложение на тендер",
    "участие в строительных тендерах",
    "строительный маркетплейс",
    "SADI Shop",
    "тендерные предложения Казахстан",
  ].join(", "),
};

export default function WorkTenderProposalPage({ params }) {
  return (
    <Layout>
      <WorkTenderProposalClient tenderId={params.id} />
    </Layout>
  );
}
