import Layout from "../../../components/Layout";
import EditTenderClient from "./EditTenderClient";

export async function generateMetadata({ params }) {
  const { id } = params;

  return {
    title: `Редактирование тендера ${id} | SADI Shop - строительные материалы по всему Казахстану`,
    description: `Редактирование тендера ${id}. Изменение условий закупки строительных материалов через SADI Shop по всему Казахстану.`,
    keywords:
      "редактирование тендера, тендер, строительные материалы, закупка, SADI, Астана, Казахстан",
    openGraph: {
      title: `Редактирование тендера ${id} | SADI Shop`,
      description: `Редактирование тендера ${id}. Изменение условий закупки строительных материалов.`,
      type: "website",
      locale: "ru_RU",
    },
  };
}

export default function EditTenderPage({ params }) {
  return (
    <Layout>
      <EditTenderClient tenderId={params.id} />
    </Layout>
  );
}
