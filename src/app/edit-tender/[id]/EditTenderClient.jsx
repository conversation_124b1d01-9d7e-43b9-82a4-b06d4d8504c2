"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import DatePicker, { registerLocale } from "react-datepicker";
import { ru } from "date-fns/locale";
import "react-datepicker/dist/react-datepicker.css";
import { useAuth } from "../../../context/AuthContext";
import authService from "../../../services/auth.service";
import API_CONFIG from "../../../config/api";

// Регистрируем русскую локализацию
registerLocale("ru", ru);

const TenderFormContainer = styled.div`
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderFormContainer.displayName = "TenderFormContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  /* border-bottom: 1px solid #dfe4e5; */
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 12px 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  border: none;
  margin-left: 20px;

  &:hover {
    background-color: #f8f9fa;
  }

  @media (max-width: 768px) {
    margin-left: -30px;
  }
`;
BackButton.displayName = "BackButton";

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;
ActionButtons.displayName = "ActionButtons";

const ClearAllButton = styled.button`
  background-color: white;
  color: #434a54;
  border: 1px solid #d6dce1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
ClearAllButton.displayName = "ClearAllButton";

const EditTenderButton = styled.button`
  background-color: #28a745;
  color: white;
  border: none;
  padding: 14px 30px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;

  transition: background-color 0.3s ease;
`;
EditTenderButton.displayName = "EditTenderButton";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`;
ProductHeader.displayName = "ProductHeader";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #969ea7;
  margin-bottom: 10px;

  @media (max-width: 768px) {
    font-size: 17px;
  }
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const RecommendedText = styled.div`
  font-size: 12px;
  color: #656d78;
  margin-top: 4px;
  font-weight: 400;
`;
RecommendedText.displayName = "RecommendedText";

const ClearAllButtonContainer = styled.div`
  display: flex;
  align-items: flex-start;
`;
ClearAllButtonContainer.displayName = "ClearAllButtonContainer";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
`;
LoadingMessage.displayName = "LoadingMessage";

const ErrorMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 16px;
  color: #dc3545;
`;
ErrorMessage.displayName = "ErrorMessage";

const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  margin-bottom: 16px;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const RemoveFileButton = styled.button`
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(220, 53, 69, 0.9);
  border: none;
  color: white;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  padding: 0;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;

  &:hover {
    background: rgba(220, 53, 69, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const DeliverySection = styled.div`
  margin-bottom: 32px;
`;
DeliverySection.displayName = "DeliverySection";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 17px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const DeliveryText = styled.p`
  font-size: 14px;
  color: #969ea7;
  margin: 8px 0 16px 0;
  line-height: 1.5;
`;
DeliveryText.displayName = "DeliveryText";

const DatePickerContainer = styled.div`
  background: #f5f5f5;
  padding: 48px 0;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;
DatePickerContainer.displayName = "DatePickerContainer";

const AddressContainer = styled.div`
  margin-bottom: 24px;
`;
AddressContainer.displayName = "AddressContainer";

const AddressRow = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;
AddressRow.displayName = "AddressRow";

const AddressInput = styled(Input)`
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  height: auto;
  padding-right: 16px;
`;
AddressInput.displayName = "AddressInput";

const CityInput = styled(AddressInput)`
  max-width: 200px;
`;
CityInput.displayName = "CityInput";

const CityDropdown = styled.div`
  position: relative;
  width: 300px;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #434a54;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }

  &:hover {
    border-color: #0066cc;
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
`;
CityDropdownList.displayName = "CityDropdownList";

const CityOption = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  color: #434a54;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;
CityOption.displayName = "CityOption";

const CityCheckbox = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${(props) => (props.checked ? "#0066cc" : "#ddd")};
  border-radius: 4px;
  background-color: ${(props) => (props.checked ? "#0066cc" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &::after {
    content: "✓";
    color: white;
    font-size: 12px;
    font-weight: bold;
    opacity: ${(props) => (props.checked ? 1 : 0)};
  }
`;
CityCheckbox.displayName = "CityCheckbox";

const PublishButton = styled(EditTenderButton)`
  font-size: 16px;
  padding: 16px 32px;
  margin-top: 32px;
`;
PublishButton.displayName = "PublishButton";

const CalendarContainer = styled.div`
  .react-datepicker {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-family: inherit;
    background: white;
    padding: 32px;
  }

  .react-datepicker__header {
    background: white;
    border-bottom: none;
    padding: 0 0 20px 0;
  }

  .react-datepicker__current-month {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .react-datepicker__navigation {
    top: 32px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      color: #e9ecef;
    }

    &--previous {
      left: 20px;
    }

    &--next {
      right: 20px;
    }
  }

  .react-datepicker__navigation-icon {
    &::before {
      border-color: #666;
      border-width: 2px 2px 0 0;
      width: 8px;
      height: 8px;
    }
  }

  .react-datepicker__day-names {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 10px;
  }

  .react-datepicker__day-name {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin: 0;
  }

  .react-datepicker__month {
  }

  .react-datepicker__week {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 0;
  }

  .react-datepicker__day {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      background: #f0f8ff;
      color: #0066cc;
    }

    &--selected {
      background: #0066cc !important;
      color: white !important;
      font-weight: 600;
    }

    &--today {
      background: #e3f2fd;
      color: #0066cc;
      font-weight: 600;
    }

    &--weekend {
      color: #dc3545;
    }

    &--outside-month {
      color: #ccc;
    }

    &--disabled {
      color: #ccc;
      cursor: not-allowed;

      &:hover {
        background: transparent;
        color: #ccc;
      }
    }
  }

  .react-datepicker__triangle {
    display: none;
  }

  @media (max-width: 768px) {
    .react-datepicker {
      padding: 16px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 32px;
      height: 32px;
      font-size: 13px;
    }

    .react-datepicker__current-month {
      font-size: 16px;
    }

    .react-datepicker__navigation {
      top: 24px;
      width: 22px;
      height: 22px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 16px;
    }
  }

  @media (max-width: 480px) {
    .react-datepicker {
      padding: 12px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }

    .react-datepicker__current-month {
      font-size: 14px;
    }

    .react-datepicker__navigation {
      top: 20px;
      width: 20px;
      height: 20px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 12px;
    }
  }
`;
CalendarContainer.displayName = "CalendarContainer";

const TenderNameContainer = styled.div`
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  gap: 16px;
`;
TenderNameContainer.displayName = "TenderNameContainer";

const TenderInputContainer = styled.div`
  position: relative;
  flex: 1;
`;
TenderInputContainer.displayName = "TenderInputContainer";

const TenderNameInput = styled(Input)`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #e0e0e0;
  }
`;
TenderNameInput.displayName = "TenderNameInput";

const TenderNumberInput = styled(Input)`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #e0e0e0;
  }
`;
TenderNumberInput.displayName = "TenderNumberInput";

const NoProductsMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoProductsMessage.displayName = "NoProductsMessage";

// Стили для уведомлений
const SuccessNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #28a745;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
SuccessNotification.displayName = "SuccessNotification";

const SuccessNotificationText = styled.span`
  flex: 1;
`;
SuccessNotificationText.displayName = "SuccessNotificationText";

const SuccessNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
SuccessNotificationClose.displayName = "SuccessNotificationClose";

const ErrorNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
ErrorNotification.displayName = "ErrorNotification";

// Стили для кнопки "Добавить еще +"
const AddMoreButton = styled.button`
  background: white;
  border: 2px solid #0066cc;
  border-radius: 8px;
  color: #0066cc;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 24px 0;
  width: 100%;
  max-width: 300px;
  transition: all 0.2s ease;

  &:hover {
    background: #f0f8ff;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;
AddMoreButton.displayName = "AddMoreButton";

// Стили для модального окна
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`;
ModalOverlay.displayName = "ModalOverlay";

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 900px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
ModalContent.displayName = "ModalContent";

const ModalHeader = styled.div`
  padding: 24px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
ModalHeader.displayName = "ModalHeader";

const ModalTitle = styled.h2`
  font-size: 24px;
  font-weight: 700;
  color: #434a54;
  margin: 0;
`;
ModalTitle.displayName = "ModalTitle";

const ModalCloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: #f8f9fa;
    color: #333;
  }
`;
ModalCloseButton.displayName = "ModalCloseButton";

const ModalBody = styled.div`
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
`;
ModalBody.displayName = "ModalBody";

// Стили для поиска в модальном окне
const SearchContainer = styled.div`
  position: relative;
  margin-bottom: 24px;
`;
SearchContainer.displayName = "SearchContainer";

const SearchInput = styled.input`
  width: 100%;
  padding: 12px 50px 12px 16px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 16px;
  color: #495057;
  background-color: #fff;

  &::placeholder {
    color: #6c757d;
  }

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;
SearchInput.displayName = "SearchInput";

const SearchIcon = styled.div`
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
`;
SearchIcon.displayName = "SearchIcon";

const ClearButton = styled.button`
  position: absolute;
  right: 46px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 18px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;

  &:hover {
    color: #495057;
  }
`;
ClearButton.displayName = "ClearButton";

// Стили для результатов поиска в модальном окне
const SearchResultsGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: opacity 0.3s ease-in-out;

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
SearchResultsGrid.displayName = "SearchResultsGrid";

const SearchResultCard = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  animation: slideInUp 0.3s ease-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;
SearchResultCard.displayName = "SearchResultCard";

const SearchResultHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;
SearchResultHeader.displayName = "SearchResultHeader";

const SearchResultCode = styled.span`
  font-size: 12px;
  color: #6c757d;
  background-color: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
`;
SearchResultCode.displayName = "SearchResultCode";

const SearchResultUnit = styled.span`
  font-size: 12px;
  color: #6c757d;
`;
SearchResultUnit.displayName = "SearchResultUnit";

const SearchResultTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 8px 0;
  line-height: 1.4;
`;
SearchResultTitle.displayName = "SearchResultTitle";

const SearchResultDescription = styled.p`
  font-size: 14px;
  color: #6c757d;
  margin: 8px 0 16px 0;
  line-height: 1.5;
`;
SearchResultDescription.displayName = "SearchResultDescription";

const AddButton = styled.button`
  background-color: ${(props) => (props.isSelected ? "#28a745" : "#007bff")};
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.isSelected ? "#218838" : "#0056b3")};
  }
`;
AddButton.displayName = "AddButton";

const NoResultsMessage = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 16px;
`;
NoResultsMessage.displayName = "NoResultsMessage";

const ResultsCounter = styled.div`
  color: #6c757d;
  font-size: 14px;
  margin-bottom: 16px;
`;
ResultsCounter.displayName = "ResultsCounter";

const EditTenderClient = ({ tenderId }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [tenderName, setTenderName] = useState("");
  const [tenderNumber, setTenderNumber] = useState("");
  const [deliveryDate, setDeliveryDate] = useState(null);
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [city, setCity] = useState("Алматы");
  const [selectedCityId, setSelectedCityId] = useState("02");
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [deliveryPriceIncluded, setDeliveryPriceIncluded] = useState(true);

  // Состояния для загрузки данных
  const [materials, setMaterials] = useState([]);
  const [tenderDetails, setTenderDetails] = useState(null);
  const [materialPhotos, setMaterialPhotos] = useState({}); // Фотографии материалов по purchReqLineId
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Состояния для уведомлений
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");

  // Состояния для отслеживания изменений и загрузки
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Состояния для модального окна поиска
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [actualSearchQuery, setActualSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isSearchLoading, setIsSearchLoading] = useState(false);
  const [searchError, setSearchError] = useState(null);

  const purchReqId = tenderId;

  // Список городов
  const cities = [
    {
      RegionId: "01",
      RegionName: "Нур-Султан",
      LivingWage: 22702,
      Coefficient: 1.155,
    },
    {
      RegionId: "02",
      RegionName: "Алматы",
      LivingWage: 22283,
      Coefficient: 1.134,
    },
    {
      RegionId: "04",
      RegionName: "Актюбинская область",
      LivingWage: 18010,
      Coefficient: 0.917,
    },
    {
      RegionId: "05",
      RegionName: "Алматинская область",
      LivingWage: 20557,
      Coefficient: 1.046,
    },
    {
      RegionId: "06",
      RegionName: "Атырауская область",
      LivingWage: 20297,
      Coefficient: 1.033,
    },
    {
      RegionId: "07",
      RegionName: "Западно-Казахстанская область",
      LivingWage: 17947,
      Coefficient: 0.913,
    },
    {
      RegionId: "08",
      RegionName: "Жамбылская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "09",
      RegionName: "Карагандинская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "10",
      RegionName: "Костанайская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "11",
      RegionName: "Кызылординская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "12",
      RegionName: "Мангистауская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "13",
      RegionName: "Туркестанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "14",
      RegionName: "Павлодарская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "15",
      RegionName: "Северо-Казахстанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "16",
      RegionName: "Восточно-Казахстанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "17",
      RegionName: "Шымкент",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "18",
      RegionName: "Акмолинская область",
      LivingWage: null,
      Coefficient: null,
    },
  ];

  // Функции для управления уведомлениями
  const showSuccess = (message) => {
    setNotificationMessage(message);
    setShowSuccessNotification(true);
    setTimeout(() => {
      setShowSuccessNotification(false);
    }, 3000);
  };

  const showError = (message) => {
    setNotificationMessage(message);
    setShowErrorNotification(true);
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 3000);
  };

  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
  };

  const handleCloseErrorNotification = () => {
    setShowErrorNotification(false);
  };

  // Функции для модального окна поиска
  const openModal = () => {
    setIsModalOpen(true);
    setSearchQuery("");
    setActualSearchQuery("");
    setSearchResults([]);
    setSearchError(null);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSearchQuery("");
    setActualSearchQuery("");
    setSearchResults([]);
    setSearchError(null);
  };

  // Функция для поиска материалов
  const searchMaterials = async (query) => {
    if (!query || query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearchLoading(true);
    setSearchError(null);

    try {
      const encodedQuery = encodeURIComponent(query);
      const url = `${API_CONFIG.BASE_URL}/api/Materials/?name=${encodedQuery}`;

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setSearchResults(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Ошибка поиска материалов:", error);
      setSearchError(error);
      setSearchResults([]);
    } finally {
      setIsSearchLoading(false);
    }
  };

  // Обработчики для поиска
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const performSearch = () => {
    if (searchQuery.trim().length >= 2) {
      setActualSearchQuery(searchQuery.trim());
      searchMaterials(searchQuery.trim());
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      performSearch();
    }
  };

  const clearSearch = () => {
    setSearchQuery("");
    setActualSearchQuery("");
    setSearchResults([]);
  };

  // Функция для добавления материала в тендер
  const handleAddMaterialToTender = async (material) => {
    try {
      // Проверяем, не добавлен ли уже этот материал
      const isAlreadyAdded = materials.some(
        (item) => item.MaterialId === material.MaterialId
      );

      if (isAlreadyAdded) {
        showError("Этот материал уже добавлен в тендер");
        return;
      }

      // Получаем токен
      const token = await authService.getAccessToken();
      if (!token) {
        showError("Токен авторизации не найден");
        return;
      }

      // Создаем новую запись материала в тендере
      const materialData = {
        PurchReqId: parseInt(purchReqId),
        ListBookId: 0,
        MaterialId: material.MaterialId,
        Code: material.Code || "",
        MaterialName: material.MaterialName,
        PurchQty: 1,
        PurchUnit: material.UnitId || "шт",
        PurchOpenPrice: 0,
        RegionId: selectedCityId,
        TermDelivery: 0,
        DescriptionDelivery: "",
        Description: "",
        ReleasePrice: 0,
        CalculatePrice: 0,
        PurchStatusLineId: 1,
        OriginalOnly: false,
        PriceWithDelivery: deliveryPriceIncluded,
      };

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/PurchReqLines`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(materialData),
      });

      if (response.ok) {
        const newMaterial = await response.json();

        // Добавляем новый материал в локальное состояние
        setMaterials((prevMaterials) => [
          ...prevMaterials,
          {
            ...materialData,
            PurchReqLineId: newMaterial.PurchReqLineId || Date.now(), // Используем ID из ответа или временный
          },
        ]);

        showSuccess("Материал успешно добавлен в тендер");
        closeModal();
      } else {
        const errorText = await response.text();
        throw new Error(
          `Ошибка добавления материала: ${response.status} - ${errorText}`
        );
      }
    } catch (error) {
      console.error("Ошибка при добавлении материала:", error);
      showError("Произошла ошибка при добавлении материала: " + error.message);
    }
  };

  // Функция для загрузки данных тендера
  const fetchTenderData = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      } else {
        throw new Error("Не удалось загрузить данные тендера");
      }
    } catch (error) {
      console.error("Ошибка при загрузке данных тендера:", error);
      throw error;
    }
  };

  // Функция для загрузки информации о тендере
  const fetchTenderInfo = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables/${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        throw new Error("Не удалось загрузить информацию о тендере");
      }
    } catch (error) {
      console.error("Ошибка при загрузке информации о тендере:", error);
      throw error;
    }
  };

  // Функция для загрузки фотографий материала
  const fetchMaterialPhotos = async (purchReqId, purchReqLineId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos?purchReqId=${purchReqId}&purchReqLineId=${purchReqLineId}`
      );
      if (response.ok) {
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      } else {
        console.warn(
          `Не удалось загрузить фотографии материала ${purchReqLineId}`
        );
        return [];
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий материала:", error);
      return [];
    }
  };

  // Функция для определения типа файла
  const getFileType = (fileName) => {
    if (!fileName) return "unknown";
    const extension = fileName.toLowerCase().split(".").pop();

    const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"];
    const documentTypes = ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"];
    const archiveTypes = ["zip", "rar", "7z", "tar", "gz"];

    if (imageTypes.includes(extension)) return "image";
    if (documentTypes.includes(extension)) return "document";
    if (archiveTypes.includes(extension)) return "archive";
    return "unknown";
  };

  // Функция для получения иконки файла
  const getFileIcon = (fileName) => {
    const extension = fileName ? fileName.toLowerCase().split(".").pop() : "";

    switch (extension) {
      case "pdf":
        return "📄";
      case "doc":
      case "docx":
        return "📝";
      case "xls":
      case "xlsx":
        return "📊";
      case "ppt":
      case "pptx":
        return "📋";
      case "zip":
      case "rar":
      case "7z":
        return "📦";
      case "txt":
        return "📃";
      default:
        return "📎";
    }
  };

  // Функция для скачивания файла
  const handleDownloadFile = (fileUrl, fileName) => {
    try {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName || "file";
      link.target = "_blank";

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Ошибка при скачивании файла:", error);
      window.open(fileUrl, "_blank");
    }
  };

  // Функция для удаления файла материала
  const handleDeleteMaterialFile = async (fileId, purchReqLineId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos/${fileId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        // Обновляем список файлов материала после удаления
        setMaterialPhotos((prevPhotos) => ({
          ...prevPhotos,
          [purchReqLineId]: (prevPhotos[purchReqLineId] || []).filter(
            (file) => file.PurchReqTableFotoId !== fileId
          ),
        }));
        console.log(`Файл ${fileId} успешно удален`);
      } else {
        throw new Error(`Ошибка удаления файла: ${response.status}`);
      }
    } catch (error) {
      console.error("Ошибка при удалении файла:", error);
      showError(`Ошибка при удалении файла: ${error.message}`);
    }
  };

  // Функция для добавления новых файлов материала
  const handleMaterialFileUpload = (files, purchReqLineId) => {
    if (files && files.length > 0) {
      const fileArray = Array.from(files);

      // Проверяем размер файлов (максимум 10MB на файл)
      const maxSize = 10 * 1024 * 1024; // 10MB
      const oversizedFiles = fileArray.filter((file) => file.size > maxSize);

      if (oversizedFiles.length > 0) {
        showError(
          `Файлы слишком большие. Максимальный размер: 10MB\n${oversizedFiles
            .map((f) => f.name)
            .join("\n")}`
        );
        return;
      }

      // Добавляем новые файлы к существующим (пока только локально)
      const newFiles = fileArray.map((file, index) => ({
        PurchReqTableFotoId: `temp_${Date.now()}_${index}`, // Временный ID
        FileName: file.name,
        FileUrl: URL.createObjectURL(file), // Временный URL для превью
        isNew: true, // Флаг для новых файлов
        file: file, // Сохраняем объект файла для загрузки
      }));

      setMaterialPhotos((prevPhotos) => ({
        ...prevPhotos,
        [purchReqLineId]: [...(prevPhotos[purchReqLineId] || []), ...newFiles],
      }));
      markAsChanged();
    }
  };

  // Функция для загрузки новых файлов материалов на сервер
  const uploadMaterialFiles = async () => {
    try {
      const uploadPromises = [];

      // Проходим по всем материалам и их файлам
      Object.entries(materialPhotos).forEach(([purchReqLineId, files]) => {
        const newFiles = files.filter((file) => file.isNew);

        newFiles.forEach((fileData) => {
          const uploadPromise = async () => {
            const formData = new FormData();
            formData.append("file", fileData.file);

            const response = await fetch(
              `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos?purchReqId=${purchReqId}&purchReqLineId=${purchReqLineId}`,
              {
                method: "POST",
                body: formData,
              }
            );

            if (!response.ok) {
              throw new Error(
                `Ошибка загрузки файла ${fileData.FileName}: ${response.status}`
              );
            }

            return await response.json();
          };

          uploadPromises.push(uploadPromise());
        });
      });

      if (uploadPromises.length > 0) {
        await Promise.all(uploadPromises);
        console.log("Все новые файлы материалов успешно загружены");
      }
    } catch (error) {
      console.error("Ошибка при загрузке файлов материалов:", error);
      throw error;
    }
  };

  // Загрузка данных при монтировании компонента
  useEffect(() => {
    const loadTenderData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Загружаем информацию о тендере и материалы
        const [tenderInfo, materialsData] = await Promise.all([
          fetchTenderInfo(),
          fetchTenderData(),
        ]);

        // Заполняем форму данными тендера
        if (tenderInfo) {
          setTenderDetails(tenderInfo); // Сохраняем полную информацию о тендере
          setTenderName(tenderInfo.PurchReqName || "");
          setTenderNumber(tenderInfo.DocNum || "");
          setDeliveryAddress(tenderInfo.DeliveryAddress || "");

          if (tenderInfo.PurchEndDate) {
            setDeliveryDate(new Date(tenderInfo.PurchEndDate));
          }

          if (tenderInfo.RegionId) {
            setSelectedCityId(tenderInfo.RegionId);
            const selectedCity = cities.find(
              (c) => c.RegionId === tenderInfo.RegionId
            );
            if (selectedCity) {
              setCity(selectedCity.RegionName);
            }
          }
        }

        // Устанавливаем материалы
        setMaterials(materialsData);

        // Загружаем фотографии для каждого материала
        if (materialsData && materialsData.length > 0) {
          const photosPromises = materialsData.map(async (material) => {
            const photos = await fetchMaterialPhotos(
              purchReqId,
              material.PurchReqLineId
            );
            return { purchReqLineId: material.PurchReqLineId, photos };
          });

          const photosResults = await Promise.all(photosPromises);
          const photosObject = {};
          photosResults.forEach(({ purchReqLineId, photos }) => {
            photosObject[purchReqLineId] = photos;
          });
          setMaterialPhotos(photosObject);
        }
      } catch (err) {
        setError(err.message);
        console.error("Ошибка при загрузке данных тендера:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (purchReqId) {
      loadTenderData();
    }
  }, [purchReqId]);

  // Функции для обработки форм
  const handleBack = () => {
    router.push("/my-tenders");
  };

  const handleCitySelect = (cityData) => {
    setCity(cityData.RegionName);
    setSelectedCityId(cityData.RegionId);
    setIsCityDropdownOpen(false);
  };

  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  // Функция для отметки изменений
  const markAsChanged = () => {
    if (!hasChanges) {
      setHasChanges(true);
    }
  };

  const handleEditTender = async () => {
    if (isSaving) return; // Предотвращаем повторные нажатия

    try {
      setIsSaving(true);

      // Проверяем авторизацию
      if (!user) {
        showError("Необходимо авторизоваться для редактирования тендера");
        return;
      }

      // Получаем токен
      const token = await authService.getAccessToken();
      if (!token) {
        showError("Токен авторизации не найден");
        return;
      }

      // Сначала обновляем основную информацию о тендере
      await updateTenderInfo(token);

      // Затем обновляем все материалы
      await updateTenderMaterials(token);

      // Загружаем новые файлы материалов (если есть)
      await uploadMaterialFiles();

      setHasChanges(false); // Сбрасываем флаг изменений
      showSuccess("Тендер успешно обновлен!");
      setTimeout(() => {
        router.push("/my-tenders");
      }, 1500);
    } catch (error) {
      console.error("Ошибка при обновлении тендера:", error);
      showError("Произошла ошибка при обновлении тендера: " + error.message);
    } finally {
      setIsSaving(false);
    }
  };

  // Функция для обновления основной информации о тендере
  const updateTenderInfo = async (token) => {
    if (!tenderDetails) {
      throw new Error("Информация о тендере не загружена");
    }

    const tenderData = {
      PurchReqId: parseInt(purchReqId),
      IdLocalQuoteOfPurchaseRequest:
        tenderDetails.IdLocalQuoteOfPurchaseRequest || 0,
      DocNum: tenderDetails.DocNum || "",
      PurchReqName: tenderName,
      CompanyId: user?.CompanyId || tenderDetails.CompanyId, // Из контекста авторизации
      UserId: user?.UserId || tenderDetails.UserId, // Из контекста авторизации
      CreateDateTime: tenderDetails.CreateDateTime,
      PurchBegDate: tenderDetails.PurchBegDate,
      PurchEndDate: deliveryDate
        ? deliveryDate.toISOString()
        : tenderDetails.PurchEndDate,
      IsPublish: tenderDetails.IsPublish,
      RegionId: selectedCityId,
      PurchStatus: tenderDetails.PurchStatus || 1,
      DocElementId: tenderDetails.DocElementId || 0,
      DocStateId: tenderDetails.DocStateId || 0,
      DeliveryAddress: deliveryAddress,
      IsSpecification: tenderDetails.IsSpecification || false,
      Description: tenderDetails.Description || "",
      CategoryId: tenderDetails.CategoryId,
    };

    const response = await fetch(
      `${API_CONFIG.BASE_URL}/api/PurchReqTables/${purchReqId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(tenderData),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Ошибка обновления тендера: ${response.status} - ${errorText}`
      );
    }
    // Для статуса 204 (No Content) не пытаемся парсить JSON
  };

  // Функция для удаления материала из тендера
  const handleRemoveProduct = async (index) => {
    try {
      const material = materials[index];
      if (!material || !material.PurchReqLineId) {
        showError("Ошибка: материал не найден");
        return;
      }

      // Получаем токен
      const token = await authService.getAccessToken();
      if (!token) {
        showError("Токен авторизации не найден");
        return;
      }

      // Отправляем DELETE запрос
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines/${material.PurchReqLineId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        // Удаляем материал из локального состояния
        const newMaterials = materials.filter((_, i) => i !== index);
        setMaterials(newMaterials);
        showSuccess("Материал успешно удален из тендера");
      } else {
        const errorText = await response.text();
        throw new Error(`Ошибка удаления: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.error("Ошибка при удалении материала:", error);
      showError("Произошла ошибка при удалении материала: " + error.message);
    }
  };

  // Функция для обновления материалов тендера
  const updateTenderMaterials = async (token) => {
    const updatePromises = materials.map(async (material) => {
      const materialData = {
        PurchReqLineId: material.PurchReqLineId,
        PurchReqId: parseInt(purchReqId),
        ListBookId: material.ListBookId || 0,
        MaterialId: material.MaterialId,
        Code: material.Code || "",
        MaterialName: material.MaterialName,
        PurchQty: parseFloat(material.PurchQty) || 0,
        PurchUnit: material.PurchUnit,
        PurchOpenPrice: parseFloat(material.PurchOpenPrice) || 0,
        RegionId: selectedCityId,
        TermDelivery: material.TermDelivery || 0,
        DescriptionDelivery: material.DescriptionDelivery || "",
        Description: material.Description || "",
        ReleasePrice: material.ReleasePrice || 0,
        CalculatePrice: material.CalculatePrice || 0,
        PurchStatusLineId: material.PurchStatusLineId || 1,
        OriginalOnly: material.OriginalOnly || false,
        PriceWithDelivery: deliveryPriceIncluded,
      };

      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines/${material.PurchReqLineId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(materialData),
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Ошибка обновления материала ${material.MaterialId}: ${response.status} - ${errorText}`
        );
      }

      // Для статуса 204 (No Content) не пытаемся парсить JSON
      return response.status === 204 ? null : response.json();
    });

    await Promise.all(updatePromises);
  };

  if (isLoading) {
    return (
      <TenderFormContainer>
        <ContentContainer>
          <LoadingMessage>Загрузка данных тендера...</LoadingMessage>
        </ContentContainer>
      </TenderFormContainer>
    );
  }

  if (error) {
    return (
      <TenderFormContainer>
        <ContentContainer>
          <ErrorMessage>Ошибка: {error}</ErrorMessage>
        </ContentContainer>
      </TenderFormContainer>
    );
  }

  return (
    <>
      {/* Уведомления */}
      {showSuccessNotification && (
        <SuccessNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseSuccessNotification}>
            ×
          </SuccessNotificationClose>
        </SuccessNotification>
      )}

      {showErrorNotification && (
        <ErrorNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseErrorNotification}>
            ×
          </SuccessNotificationClose>
        </ErrorNotification>
      )}

      <TenderFormContainer>
        <Header>
          <HeaderContent>
            <BackButton onClick={handleBack}>
              <img
                src="/icons/arrow_back_24px.svg"
                alt="Назад"
                style={{ width: "12px", height: "12px" }}
              />
              НАЗАД К МОИМ ТЕНДЕРАМ
            </BackButton>
            <ActionButtons>
              {/* <ClearAllButton>
              <img src="/icons/clear_all_24px.svg" alt="Очистить" />
              ОЧИСТИТЬ ВСЕ
            </ClearAllButton> */}
            </ActionButtons>
          </HeaderContent>
        </Header>

        <ContentContainer>
          <Title>Редактирование тендера</Title>

          <SectionTitle>Основная информация</SectionTitle>
          <TenderNameContainer>
            <TenderInputContainer>
              <TenderNameInput
                type="text"
                placeholder="Название тендера"
                value={tenderName}
                onChange={(e) => {
                  setTenderName(e.target.value);
                  markAsChanged();
                }}
              />
            </TenderInputContainer>
            <TenderInputContainer style={{ maxWidth: "200px" }}>
              <TenderNumberInput
                type="text"
                placeholder="Номер тендера"
                value={tenderNumber}
                onChange={(e) => setTenderNumber(e.target.value)}
                disabled
                style={{ backgroundColor: "#f8f9fa", color: "#666" }}
              />
            </TenderInputContainer>
          </TenderNameContainer>

          <SectionTitle>Срок поставки</SectionTitle>
          <DatePickerContainer>
            <CalendarContainer>
              <DatePicker
                selected={deliveryDate}
                onChange={(date) => {
                  setDeliveryDate(date);
                  markAsChanged();
                }}
                dateFormat="dd MMMM yyyy"
                locale="ru"
                calendarStartDay={1}
                inline
                minDate={new Date()}
              />
            </CalendarContainer>
          </DatePickerContainer>

          <SectionTitle>Адрес доставки</SectionTitle>
          <AddressContainer>
            <DeliveryText>
              Укажите адрес, куда нужно доставить товары
            </DeliveryText>
            <AddressRow>
              <AddressInput
                type="text"
                placeholder="Адрес доставки"
                value={deliveryAddress}
                onChange={(e) => {
                  setDeliveryAddress(e.target.value);
                  markAsChanged();
                }}
              />
              <CityDropdown>
                <CityButton onClick={toggleCityDropdown}>
                  {city}
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{
                      transform: isCityDropdownOpen
                        ? "rotate(180deg)"
                        : "rotate(0)",
                      transition: "transform 0.3s ease",
                    }}
                  >
                    <path
                      d="M4 6L8 10L12 6"
                      stroke="#333"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </CityButton>
                {isCityDropdownOpen && (
                  <CityDropdownList>
                    {cities.map((cityData) => (
                      <CityOption
                        key={cityData.RegionId}
                        onClick={() => handleCitySelect(cityData)}
                      >
                        <span>{cityData.RegionName}</span>
                        <CityCheckbox
                          checked={selectedCityId === cityData.RegionId}
                        />
                      </CityOption>
                    ))}
                  </CityDropdownList>
                )}
              </CityDropdown>
            </AddressRow>
          </AddressContainer>

          <SectionTitle>Учет цены доставки</SectionTitle>
          <DeliverySection>
            <ActionButtonContainer>
              <ActionButton
                active={deliveryPriceIncluded}
                onClick={() => setDeliveryPriceIncluded(true)}
              >
                Цена с доставкой
              </ActionButton>
              <ActionButton
                active={!deliveryPriceIncluded}
                onClick={() => setDeliveryPriceIncluded(false)}
              >
                Цена без доставки
              </ActionButton>
            </ActionButtonContainer>
            <DeliveryText>
              Выберите, нужно ли поставщикам включать стоимость доставки в цену
            </DeliveryText>
          </DeliverySection>

          <SectionTitle>Список закупок</SectionTitle>
          <Text>Материалы, которые были добавлены в тендер</Text>

          {/* Кнопка "Добавить еще +" */}
          <AddMoreButton onClick={openModal}>Добавить еще +</AddMoreButton>

          {materials.length === 0 ? (
            <NoProductsMessage>
              В тендере нет материалов для отображения
            </NoProductsMessage>
          ) : (
            materials.map((material, index) => (
              <ProductFormCard key={material.MaterialId}>
                <ProductHeader>
                  <ProductInfo>
                    <ProductId>{material.MaterialId}</ProductId>
                    <Label>Единица измерения: {material.PurchUnit}</Label>
                    <ProductTitle>{material.MaterialName}</ProductTitle>
                    {/* <Label style={{ marginBottom: 0 }}>
                      {material.Description ||
                        "Продается в штуках, используется для строительных работ."}
                    </Label> */}
                  </ProductInfo>
                  <ClearAllButtonContainer>
                    <ClearAllButton
                      onClick={() => handleRemoveProduct(index)}
                      style={{ padding: "12px" }}
                    >
                      <img
                        src="/icons/BusketCreateTender.svg"
                        width={"16"}
                        height={"16"}
                        alt="Удалить"
                      />
                    </ClearAllButton>
                  </ClearAllButtonContainer>
                </ProductHeader>

                <FormRow>
                  <SmallFormGroup>
                    <Input
                      type="number"
                      value={material.PurchQty || ""}
                      onChange={(e) => {
                        const newMaterials = [...materials];
                        newMaterials[index].PurchQty = e.target.value;
                        setMaterials(newMaterials);
                        markAsChanged();
                      }}
                      placeholder="Кол-во"
                      required
                    />
                    <span
                      style={{
                        position: "absolute",
                        right: "8px",
                        bottom: "8px",
                        color: "#656D78",
                        fontSize: "17px",
                        fontWeight: "400",
                      }}
                    >
                      {material.PurchUnit}.
                    </span>
                  </SmallFormGroup>

                  <SmallFormGroup>
                    <Input
                      type="number"
                      value={material.PurchOpenPrice || ""}
                      onChange={(e) => {
                        const newMaterials = [...materials];
                        newMaterials[index].PurchOpenPrice = e.target.value;
                        setMaterials(newMaterials);
                        markAsChanged();
                      }}
                      placeholder="Ваша цена"
                      style={{ paddingRight: "77px" }}
                    />
                    <span
                      style={{
                        position: "absolute",
                        right: "8px",
                        bottom: "8px",
                        color: "#656D78",
                        fontSize: "17px",
                        fontWeight: "400",
                      }}
                    >
                      ₸ за {material.PurchUnit}.
                    </span>
                  </SmallFormGroup>

                  <ActionButtonContainer>
                    <ActionButton
                      active={material.OriginalOnly === false}
                      onClick={() => {
                        const newMaterials = [...materials];
                        newMaterials[index].OriginalOnly = false;
                        setMaterials(newMaterials);
                      }}
                    >
                      Можно аналог
                    </ActionButton>
                    <ActionButton
                      active={material.OriginalOnly === true}
                      onClick={() => {
                        const newMaterials = [...materials];
                        newMaterials[index].OriginalOnly = true;
                        setMaterials(newMaterials);
                      }}
                    >
                      Только это
                    </ActionButton>
                  </ActionButtonContainer>
                </FormRow>

                <Label style={{ fontSize: "17px", color: "#656D78" }}>
                  Пожелания к заказу
                </Label>

                <TextArea
                  value={material.Description || ""}
                  onChange={(e) => {
                    const newMaterials = [...materials];
                    newMaterials[index].Description = e.target.value;
                    setMaterials(newMaterials);
                    markAsChanged();
                  }}
                  placeholder="Дополнительные требования к товару"
                />

                {/* Секция для файлов материала */}
                <div style={{ marginTop: "16px" }}>
                  <Label
                    style={{
                      fontSize: "17px",
                      color: "#656D78",
                      marginBottom: "8px",
                    }}
                  >
                    Прикрепленные файлы
                  </Label>

                  <input
                    type="file"
                    multiple
                    style={{ display: "none" }}
                    id={`material-file-input-${material.PurchReqLineId}`}
                    onChange={(e) =>
                      handleMaterialFileUpload(
                        e.target.files,
                        material.PurchReqLineId
                      )
                    }
                  />

                  <UploadButton
                    onClick={() =>
                      document
                        .getElementById(
                          `material-file-input-${material.PurchReqLineId}`
                        )
                        .click()
                    }
                    style={{ marginBottom: "12px" }}
                  >
                    <img src="/icons/Upload.svg" alt="Загрузить" />
                    Прикрепить файлы
                  </UploadButton>

                  {/* Отображение файлов материала */}
                  {materialPhotos[material.PurchReqLineId] &&
                    materialPhotos[material.PurchReqLineId].length > 0 && (
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "wrap",
                          gap: "12px",
                        }}
                      >
                        {materialPhotos[material.PurchReqLineId].map(
                          (file, fileIndex) => {
                            const fileType = getFileType(file.FileName);
                            const isImage = fileType === "image";

                            return (
                              <div
                                key={file.PurchReqTableFotoId}
                                style={{ position: "relative" }}
                              >
                                {isImage ? (
                                  <img
                                    src={file.FileUrl}
                                    alt={`Файл материала ${fileIndex + 1}`}
                                    style={{
                                      width: "120px",
                                      height: "120px",
                                      objectFit: "cover",
                                      borderRadius: "4px",
                                      border: "1px solid #ddd",
                                      cursor: "pointer",
                                    }}
                                    onClick={() =>
                                      handleDownloadFile(
                                        file.FileUrl,
                                        file.FileName
                                      )
                                    }
                                    onError={(e) => {
                                      e.target.style.display = "none";
                                    }}
                                  />
                                ) : (
                                  <div
                                    style={{
                                      width: "120px",
                                      height: "120px",
                                      borderRadius: "4px",
                                      border: "1px solid #ddd",
                                      display: "flex",
                                      flexDirection: "column",
                                      alignItems: "center",
                                      justifyContent: "center",
                                      backgroundColor: "#f8f9fa",
                                      cursor: "pointer",
                                    }}
                                    onClick={() =>
                                      handleDownloadFile(
                                        file.FileUrl,
                                        file.FileName
                                      )
                                    }
                                  >
                                    <div
                                      style={{
                                        fontSize: "36px",
                                        marginBottom: "4px",
                                      }}
                                    >
                                      {getFileIcon(file.FileName)}
                                    </div>
                                    <div
                                      style={{
                                        fontSize: "9px",
                                        textAlign: "center",
                                        padding: "0 2px",
                                        color: "#666",
                                        wordBreak: "break-word",
                                      }}
                                    >
                                      {file.FileName}
                                    </div>
                                  </div>
                                )}

                                {/* Кнопка удаления */}
                                <RemoveFileButton
                                  onClick={() =>
                                    handleDeleteMaterialFile(
                                      file.PurchReqTableFotoId,
                                      material.PurchReqLineId
                                    )
                                  }
                                  title="Удалить файл"
                                >
                                  ✕
                                </RemoveFileButton>

                                {/* Название файла для изображений */}
                                {isImage && (
                                  <div
                                    style={{
                                      position: "absolute",
                                      bottom: "4px",
                                      left: "4px",
                                      right: "4px",
                                      background: "rgba(0,0,0,0.7)",
                                      color: "white",
                                      fontSize: "8px",
                                      padding: "2px",
                                      borderRadius: "2px",
                                      textAlign: "center",
                                    }}
                                  >
                                    {file.FileName}
                                  </div>
                                )}
                              </div>
                            );
                          }
                        )}
                      </div>
                    )}
                </div>
              </ProductFormCard>
            ))
          )}

          <EditTenderButton
            onClick={handleEditTender}
            disabled={!hasChanges || isSaving}
            style={{
              opacity: !hasChanges || isSaving ? 0.5 : 1,
              cursor: !hasChanges || isSaving ? "not-allowed" : "pointer",
            }}
          >
            {isSaving ? "СОХРАНЕНИЕ..." : "СОХРАНИТЬ ТЕНДЕР"}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04a1 1 0 0 0 0-1.41l-2.34-2.34a1 1 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z" />
            </svg>
          </EditTenderButton>
        </ContentContainer>
      </TenderFormContainer>

      {/* Модальное окно для поиска материалов */}
      {isModalOpen && (
        <ModalOverlay onClick={closeModal}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Добавить материал в тендер</ModalTitle>
              <ModalCloseButton onClick={closeModal}>×</ModalCloseButton>
            </ModalHeader>
            <ModalBody>
              {/* Поиск */}
              <SearchContainer>
                <SearchInput
                  type="text"
                  placeholder="Поиск материалов... (нажмите Enter для поиска)"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onKeyDown={handleKeyDown}
                />
                {searchQuery && (
                  <ClearButton onClick={clearSearch}>×</ClearButton>
                )}
                <SearchIcon
                  onClick={performSearch}
                  style={{ cursor: "pointer" }}
                >
                  <img src="/icons/search.svg" alt="Поиск" />
                </SearchIcon>
              </SearchContainer>

              {/* Результаты поиска */}
              {actualSearchQuery.trim() ? (
                <>
                  {/* Счетчик результатов */}
                  {!isSearchLoading && !searchError && (
                    <ResultsCounter>
                      {searchResults.length === 0
                        ? "Ничего не найдено"
                        : `Найдено: ${searchResults.length} ${
                            searchResults.length === 1
                              ? "товар"
                              : searchResults.length < 5
                              ? "товара"
                              : "товаров"
                          }`}
                    </ResultsCounter>
                  )}

                  {isSearchLoading ? (
                    <NoResultsMessage>Поиск товаров...</NoResultsMessage>
                  ) : searchError ? (
                    <NoResultsMessage>
                      Ошибка поиска: {searchError.message}
                    </NoResultsMessage>
                  ) : searchResults.length === 0 ? (
                    <NoResultsMessage>
                      По запросу "{actualSearchQuery}" ничего не найдено
                    </NoResultsMessage>
                  ) : (
                    <SearchResultsGrid>
                      {searchResults.map((product, index) => (
                        <SearchResultCard
                          key={product.MaterialId}
                          style={{
                            animationDelay: `${index * 0.05}s`,
                          }}
                        >
                          <SearchResultHeader>
                            <SearchResultCode>
                              {product.MaterialId}
                            </SearchResultCode>
                            <SearchResultUnit>
                              Единица измерения: {product.UnitId}
                            </SearchResultUnit>
                          </SearchResultHeader>
                          <SearchResultTitle>
                            {product.MaterialName}
                          </SearchResultTitle>
                          <SearchResultDescription>
                            Продается в штуках, используется для выкладки стен и
                            иных несущих конструкций.
                          </SearchResultDescription>

                          <AddButton
                            isSelected={materials.some(
                              (item) => item.MaterialId === product.MaterialId
                            )}
                            onClick={() => handleAddMaterialToTender(product)}
                            disabled={materials.some(
                              (item) => item.MaterialId === product.MaterialId
                            )}
                          >
                            {materials.some(
                              (item) => item.MaterialId === product.MaterialId
                            )
                              ? "Уже добавлен"
                              : "Добавить"}
                          </AddButton>
                        </SearchResultCard>
                      ))}
                    </SearchResultsGrid>
                  )}
                </>
              ) : (
                <NoResultsMessage>
                  Введите название материала в поисковую строку выше
                </NoResultsMessage>
              )}
            </ModalBody>
          </ModalContent>
        </ModalOverlay>
      )}
    </>
  );
};

export default EditTenderClient;
