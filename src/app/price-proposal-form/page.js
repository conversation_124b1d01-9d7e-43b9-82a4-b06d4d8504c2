import Layout from "../../components/Layout";
import PriceProposalFormClient from "./PriceProposalFormClient";

export const metadata = {
  title:
    "Форма ценового предложения | SADI Shop - строительный маркетплейс по всему Казахстану",
  description:
    "Создайте ценовое предложение на материалы, инструменты и оборудование в SADI Shop - строительный маркетплейс по всему Казахстану",
  keywords:
    "форма ценового предложения, создать предложение, розничная цена, оптовая цена, прайс лист, калькулятор цен, стоимость материалов, расчет стоимости, коммерческое предложение, смета, бюджет, финансирование, инвестиции, капитальные вложения, строительные материалы, отделочные материалы, кровельные материалы, фасадные материалы, теплоизоляция, гидроизоляция, звукоизоляция, напольные покрытия, настенные покрытия, потолочные материалы, лестницы, перила, ограждения, заборы, ворота, калитки, металлоконструкции, железобетонные изделия, деревянные конструкции, пластиковые изделия, композитные материалы, инновационные технологии, энергосбережение, экология, безопасность, качество, надежность, долговечность, эстетика, дизайн, архитектура, проектирование, строительство домов, ремонт квартир, реконструкция зданий, модернизация, благоустройство, ландшафтный дизайн, озеленение, мощение, дренаж, водоснабжение, канализация, отопление, вентиляция, кондиционирование, электроснабжение, слаботочные системы, автоматизация, умный дом, Астана, Нур-Султан, Алматы, Шымкент, Караганда, Актобе, Тараз, Павлодар, Усть-Каменогорск, Семей, Атырау, Костанай, Кызылорда, Уральск, Петропавловск, Актау, Темиртау, Туркестан, Кокшетау, Талдыкорган, Экибастуз, Рудный, Жезказган, Балхаш, Казахстан, регионы Казахстана, области Казахстана, города Казахстана, поставщики, производители, дистрибьюторы, дилеры, представители, агенты, консультанты, эксперты, специалисты, профессионалы, мастера, бригады, подрядчики, субподрядчики, генподрядчики, девелоперы, инвесторы, заказчики, клиенты, покупатели, потребители",
};

export default function PriceProposalFormPage() {
  return (
    <Layout>
      <PriceProposalFormClient />
    </Layout>
  );
}
