import Layout from "../../../components/Layout";
import TenderProposalClient from "./TenderProposalClient";

export async function generateMetadata({ params }) {
  const { id } = params;

  return {
    title: `Подача предложения на тендер ${id} | SADI Shop — строительный маркетплейс по всему Казахстану`,
    description: `Подайте предложение на тендер ${id}. Укажите цены и условия поставки строительных материалов через SADI Shop — строительный маркетплейс, работающий по всему Казахстану.`,
    keywords: [
      // Основные термины
      "тендер предложение",
      "подача предложения",
      "ценовое предложение",

      // Материалы (топ-запросы)
      "цемент предложение",
      "кирпич тендер",
      "арматура цена",
      "бетон поставка",
      "песок предложение",
      "щебень тендер",
      "плитка цена",
      "краска поставка",

      // Города (приоритетные)
      "Алматы",
      "Астана",
      "Нур-Султан",
      "Шымкент",
      "Караганда",
      "Актобе",
      "Тараз",
      "Павлодар",
      "Усть-Каменогорск",
      "Семей",
      "Атырау",
      "Костанай",
      "Кызылорда",
      "Уральск",
      "Петропавловск",

      // Коммерческие запросы
      "поставщик материалов",
      "строительная компания",
      "оптовая торговля",
      "дистрибьютор",
      "производитель стройматериалов",
      "импортер",
      "экспортер",
      "торговый дом",

      // Длинный хвост
      "как подать предложение на тендер",
      "участие в строительных тендерах",
      "строительный маркетплейс",
      "SADI Shop",
      "тендерные предложения Казахстан",
    ].join(", "),
    openGraph: {
      title: `Подача предложения на тендер ${id} | SADI Shop`,
      description: `Подайте предложение на тендер ${id}. Укажите цены и условия поставки строительных материалов.`,
      type: "website",
      locale: "ru_KZ",
    },
  };
}

export default function TenderProposalPage({ params }) {
  return (
    <Layout>
      <TenderProposalClient tenderId={params.id} />
    </Layout>
  );
}
