import { notFound } from "next/navigation";
import Layout from "../../../components/Layout";
import ProductDetailClient from "./ProductDetailClient";
import Breadcrumbs from "../../../components/Breadcrumbs";
import ApiService from "../../../services/api.service";
import ISR_CONFIG, { ISRUtils } from "../../../config/isr";
import {
  generateSmartKeywords,
  generateLocalKeywords,
} from "../../../utils/keywordGenerator";
import {
  ProductSchema,
  BreadcrumbSchema,
} from "../../../components/StructuredData";

// ISR: Генерация статических путей для популярных товаров
export async function generateStaticParams() {
  try {
    // Генерируем только самые популярные товары при билде
    const limit = ISR_CONFIG.STATIC_GENERATION_LIMITS.PRODUCTS_PREBUILD;
    const response = await ApiService.getProductsWithPagination(1, limit);
    const products = response.data || response.products || [];

    ISRUtils.logISROperation("generateStaticParams", {
      type: "products",
      generated: products.length,
      limit,
    });

    return products.map((product) => ({
      id: product.MaterialId.toString(),
    }));
  } catch (error) {
    console.error("ISR: Ошибка при генерации статических путей:", error);
    // Возвращаем пустой массив, все страницы будут генерироваться по требованию
    return [];
  }
}

// ISR: Настройка времени ревалидации (30 дней)
export const revalidate = 2592000; // 30 дней в секундах

// Генерация метаданных для SEO
export async function generateMetadata({ params }) {
  const productId = params.id;

  try {
    // Получаем данные о товаре на сервере
    const product = await ApiService.getProductById(productId);

    if (!product) {
      return {
        title: "Товар не найден",
        description: "Запрашиваемый товар не найден в каталоге",
      };
    }

    // Генерируем SEO-оптимизированные мета-теги
    const materialName = product.MaterialName.toLowerCase();

    // Улучшенный заголовок с эмоциональными триггерами
    const priceText = product.RetailPrice
      ? `от ${product.RetailPrice.toLocaleString()} ₸`
      : "по выгодной цене";

    const title = `${product.MaterialName} купить в Казахстане ${priceText} Доставка | SADI Shop`;

    // Улучшенное описание с призывом к действию
    const description = ` ${product.MaterialName} ${priceText} в Казахстане. ${
      product.SuppliersCount || 0
    } проверенных поставщиков. Быстрая доставка по всему Казахстану. Гарантия качества. Заказать онлайн!`;

    // НОВАЯ УМНАЯ СИСТЕМА генерации ключевых слов для 100,000+ товаров
    const generateKeywords = (materialName) => {
      const baseKeywords = [
        product.MaterialName,
        `купить ${materialName}`,
        `${materialName} цена`,
        `${materialName} казахстан`,
        `${materialName} доставка`,
        `заказать ${materialName}`,
        `${materialName} оптом`,
        `${materialName} розница`,
      ];

      // Используем умную систему генерации ключевых слов
      const smartKeywords = generateSmartKeywords(materialName);
      baseKeywords.push(...smartKeywords);

      // Добавляем локальные ключевые слова
      const localKeywords = generateLocalKeywords(materialName);
      baseKeywords.push(...localKeywords);

      // Убираем дубликаты и возвращаем уникальные ключевые слова
      return [...new Set(baseKeywords)];
    };

    return {
      title,
      description,
      keywords: generateKeywords(materialName),
      openGraph: {
        title,
        description,
        type: "website",
        url: `https://shop.sadi.kz/product/${productId}`,
        siteName: "SADI Shop",
        images: [
          {
            url: "/images/placeholder.png",
            width: 800,
            height: 600,
            alt: product.MaterialName,
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: ["/images/placeholder.png"],
      },
      alternates: {
        canonical: `https://shop.sadi.kz/product/${productId}`,
      },
      robots: {
        index: true,
        follow: true,
      },
      other: {
        // Кастомные OpenGraph теги для товара
        "og:type": "product",
        "product:price:amount": product.RetailPrice || 0,
        "product:price:currency": "KZT",
        "product:availability": "in stock",
        "product:condition": "new",
        "product:retailer_item_id": product.MaterialId,
      },
    };
  } catch (error) {
    console.error("Ошибка при генерации метаданных товара:", error);
    return {
      title: "Товар не найден",
      description: "Запрашиваемый товар не найден в каталоге",
    };
  }
}

// Серверный компонент для получения данных товара
async function getProductData(productId) {
  try {
    console.log("SSR Product: Загружаем данные товара", productId);

    const product = await ApiService.getProductById(productId);

    if (!product) {
      return null;
    }

    console.log("SSR Product: Товар загружен:", product.MaterialName);
    return product;
  } catch (error) {
    console.error("SSR Product: Ошибка при получении данных товара:", error);
    return null;
  }
}

// Основной серверный компонент страницы
export default async function ProductPage({ params }) {
  const productId = params.id;

  // Валидация ID товара
  if (!productId) {
    notFound();
  }

  // Получаем данные товара на сервере
  const product = await getProductData(productId);

  // Если товар не найден, показываем 404
  if (!product) {
    notFound();
  }

  // Формируем расширенные структурированные данные для поисковых систем
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.MaterialName,
    description:
      product.Description ||
      `${product.MaterialName} - строительный материал высокого качества. Купить с доставкой по Казахстану.`,
    sku: product.MaterialId.toString(),
    mpn: product.MaterialId.toString(), // Manufacturer Part Number
    gtin: product.MaterialId.toString(), // Global Trade Item Number
    brand: {
      "@type": "Brand",
      name: "SADI Shop",
    },
    category: "Строительные материалы",
    offers: {
      "@type": "Offer",
      price: product.RetailPrice || 0,
      priceCurrency: "KZT",
      availability: "https://schema.org/InStock",
      priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0], // 30 дней
      itemCondition: "https://schema.org/NewCondition",
      seller: {
        "@type": "Organization",
        name: "Строительный маркетплейс",
        url: "https://shop.sadi.kz",
      },
      shippingDetails: {
        "@type": "OfferShippingDetails",
        shippingRate: {
          "@type": "MonetaryAmount",
          value: "0",
          currency: "KZT",
        },
        deliveryTime: {
          "@type": "ShippingDeliveryTime",
          handlingTime: {
            "@type": "QuantitativeValue",
            minValue: 1,
            maxValue: 3,
            unitCode: "DAY",
          },
        },
      },
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.5",
      reviewCount: "10",
      bestRating: "5",
      worstRating: "1",
    },
    review: [
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: {
          "@type": "Person",
          name: "Строительная компания",
        },
        reviewBody: `Качественный ${product.MaterialName}. Быстрая доставка, соответствует заявленным характеристикам.`,
      },
    ],
    image: "/images/placeholder.png",
    url: `https://shop.sadi.kz/product/${productId}`,
    additionalProperty: [
      {
        "@type": "PropertyValue",
        name: "Материал",
        value: product.MaterialName,
      },
      {
        "@type": "PropertyValue",
        name: "Поставщики",
        value: `${product.SuppliersCount || 0} поставщиков`,
      },
      {
        "@type": "PropertyValue",
        name: "Артикул",
        value: product.MaterialId.toString(),
      },
      {
        "@type": "PropertyValue",
        name: "Единица измерения",
        value: product.UnitName || "шт",
      },
      {
        "@type": "PropertyValue",
        name: "Страна производства",
        value: "Казахстан",
      },
    ],
    // Добавляем информацию о доставке
    hasMerchantReturnPolicy: {
      "@type": "MerchantReturnPolicy",
      applicableCountry: "KZ",
      returnPolicyCategory:
        "https://schema.org/MerchantReturnFiniteReturnWindow",
      merchantReturnDays: 14,
    },
  };

  return (
    <Layout>
      {/* Структурированные данные для поисковых систем */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Улучшенные хлебные крошки с микроразметкой */}
      <Breadcrumbs
        customBreadcrumbs={[
          { name: "Главная", url: "https://shop.sadi.kz" },
          {
            name: "Каталог товаров",
            url: "https://shop.sadi.kz/products/page/1",
          },
          {
            name: product.MaterialName,
            url: `https://shop.sadi.kz/product/${productId}`,
          },
        ]}
      />

      {/* Клиентский компонент для интерактивности */}
      <ProductDetailClient product={product} />
    </Layout>
  );
}
