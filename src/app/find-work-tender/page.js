import Layout from "../../components/Layout";
import FindWorkTenderClient from "./FindWorkTenderClient";

export const metadata = {
  title:
    "Найти тендер на работы | SADI Shop — строительный маркетплейс по всему Казахстану",
  description:
    "Найдите тендеры на строительные работы в SADI Shop - строительный маркетплейс по всему Казахстану",
  keywords: [
    // Основные термины
    "найти тендер",
    "тендер на работы",
    "строительные работы тендер",

    // Виды работ (топ-запросы)
    "строительство домов тендер",
    "ремонт квартир тендер",
    "кровельные работы тендер",
    "отделочные работы тендер",
    "электромонтажные работы",
    "сантехнические работы",

    // Города (приоритетные)
    "Алматы",
    "Астана",
    "Нур-Султан",
    "Шымкент",
    "Караганда",
    "Актобе",
    "Тараз",
    "Павлодар",
    "Усть-Каменогорск",
    "Семей",
    "Атырау",
    "Костанай",
    "Кызылорда",
    "Уральск",
    "Петропавловск",

    // Коммерческие запросы
    "подрядчик строительных работ",
    "строительная компания",
    "исполнитель работ",
    "тендер на подряд",
    "государственные закупки",
    "частные тендеры",

    // Длинный хвост
    "где найти тендеры на строительные работы",
    "строительные тендеры Казахстан",
    "строительный маркетплейс",
    "SADI Shop",
    "поиск строительных подрядов",
  ].join(", "),
};

export default function FindWorkTenderPage() {
  return (
    <Layout>
      <FindWorkTenderClient />
    </Layout>
  );
}
