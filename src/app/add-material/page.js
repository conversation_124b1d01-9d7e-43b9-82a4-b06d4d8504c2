import Layout from "../../components/Layout";
import AddMaterialClient from "./AddMaterialClient";

export const metadata = {
  title:
    "Добавить материал | SADI Shop — строительный маркетплейс по всему Казахстану",
  description:
    "Подайте заявку на добавление нового материала в каталог SADI Shop — строительного маркетплейса, работающего по всему Казахстану",
  keywords: [
    // Основные термины
    "добавить материал",
    "заявка на материал",
    "новый товар в каталог",

    // Материалы (топ-запросы)
    "цемент добавить",
    "кирпич в каталог",
    "арматура заявка",
    "бетон новый товар",
    "песок добавить",
    "щебень в каталог",
    "плитка заявка",
    "краска новый товар",

    // Города (приоритетные)
    "Алматы",
    "Астана",
    "Нур-Султан",
    "Шымкент",
    "Караганда",
    "Актобе",
    "Тараз",
    "Павлодар",
    "Усть-Каменогорск",
    "Семей",
    "Атырау",
    "Костанай",
    "Кызылорда",
    "Уральск",
    "Петропавловск",

    // Коммерческие запросы
    "поставщик материалов",
    "производитель стройматериалов",
    "оптовая торговля",
    "строительная компания",
    "дистрибьютор",
    "импортер материалов",

    // Длинный хвост
    "как добавить товар на маркетплейс",
    "регистрация поставщика стройматериалов",
    "строительный маркетплейс Казахстан",
    "SADI Shop",
    "каталог строительных материалов",
  ].join(", "),
};

export default function AddMaterialPage() {
  return (
    <Layout>
      <AddMaterialClient />
    </Layout>
  );
}
