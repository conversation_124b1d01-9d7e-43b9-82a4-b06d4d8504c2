// Компонент для локального SEO оптимизации

import { LocalBusinessSchema } from "./StructuredData";

// Данные о городах Казахстана для локального SEO
export const KAZAKHSTAN_CITIES = [
  {
    name: "Астана",
    region: "Акмолинская область",
    coordinates: { lat: 51.1694, lng: 71.4491 },
    population: 1350000,
    keywords: ["астана", "нур-султан", "столица казахстана"]
  },
  {
    name: "Алматы",
    region: "Алматинская область", 
    coordinates: { lat: 43.2220, lng: 76.8512 },
    population: 2000000,
    keywords: ["алматы", "алма-ата", "южная столица"]
  },
  {
    name: "Шымкент",
    region: "Туркестанская область",
    coordinates: { lat: 42.3000, lng: 69.5900 },
    population: 1000000,
    keywords: ["шымкент", "чимкент", "южный казахстан"]
  },
  {
    name: "Караганда",
    region: "Карагандинская область",
    coordinates: { lat: 49.8047, lng: 73.1094 },
    population: 500000,
    keywords: ["караганда", "центральный казахстан"]
  },
  {
    name: "Актобе",
    region: "Актюбинская область",
    coordinates: { lat: 50.2839, lng: 57.1670 },
    population: 500000,
    keywords: ["актобе", "актюбинск", "западный казахстан"]
  },
  {
    name: "Тараз",
    region: "Жамбылская область",
    coordinates: { lat: 42.9000, lng: 71.3667 },
    population: 350000,
    keywords: ["тараз", "джамбул"]
  },
  {
    name: "Павлодар",
    region: "Павлодарская область",
    coordinates: { lat: 52.2873, lng: 76.9674 },
    population: 350000,
    keywords: ["павлодар", "северный казахстан"]
  },
  {
    name: "Усть-Каменогорск",
    region: "Восточно-Казахстанская область",
    coordinates: { lat: 49.9483, lng: 82.6283 },
    population: 300000,
    keywords: ["усть-каменогорск", "восточный казахстан"]
  },
  {
    name: "Семей",
    region: "Восточно-Казахстанская область",
    coordinates: { lat: 50.4111, lng: 80.2275 },
    population: 350000,
    keywords: ["семей", "семипалатинск"]
  },
  {
    name: "Атырау",
    region: "Атырауская область",
    coordinates: { lat: 47.1164, lng: 51.8815 },
    population: 350000,
    keywords: ["атырау", "гурьев", "каспийское море"]
  }
];

// Генерация локальных ключевых слов
export const generateLocalKeywords = (baseKeywords, city) => {
  const cityData = KAZAKHSTAN_CITIES.find(c => c.name === city);
  if (!cityData) return baseKeywords;

  const localKeywords = [
    ...cityData.keywords.map(keyword => `${baseKeywords} ${keyword}`),
    `${baseKeywords} ${city}`,
    `${baseKeywords} ${cityData.region}`,
    `купить ${baseKeywords} ${city}`,
    `заказать ${baseKeywords} ${city}`,
    `доставка ${baseKeywords} ${city}`,
    `поставщики ${baseKeywords} ${city}`,
    `цены ${baseKeywords} ${city}`,
    `магазин ${baseKeywords} ${city}`,
  ];

  return `${baseKeywords}, ${localKeywords.join(", ")}`;
};

// Генерация локального контента
export const generateLocalContent = (city, category = "строительные материалы") => {
  const cityData = KAZAKHSTAN_CITIES.find(c => c.name === city);
  if (!cityData) return null;

  return {
    title: `${category} в ${city} - купить с доставкой | SADI Shop`,
    description: `Купить ${category} в ${city} с доставкой. SADI Shop - интернет-магазин строительных материалов в ${cityData.region}. Лучшие цены, проверенные поставщики.`,
    h1: `${category} в ${city}`,
    localText: `
      SADI Shop предлагает широкий выбор ${category} в ${city} и ${cityData.region}. 
      Мы работаем с проверенными поставщиками и обеспечиваем быструю доставку по ${city}. 
      ${cityData.population > 500000 ? 'Как один из крупнейших городов Казахстана, ' : ''}
      ${city} является важным центром строительной индустрии региона.
    `,
    advantages: [
      `Доставка по всему ${city}`,
      `Работаем в ${cityData.region}`,
      `Местные поставщики в ${city}`,
      `Склады в ${city}`,
      `Техподдержка в ${city}`
    ]
  };
};

// Компонент для отображения локального контента
export function LocalContent({ city, category }) {
  const content = generateLocalContent(city, category);
  
  if (!content) return null;

  return (
    <div style={{ 
      backgroundColor: '#f8f9fa', 
      padding: '30px', 
      borderRadius: '8px', 
      marginTop: '40px' 
    }}>
      <h2 style={{ color: '#333', marginBottom: '20px' }}>
        {content.h1}
      </h2>
      
      <p style={{ marginBottom: '20px', lineHeight: '1.6' }}>
        {content.localText}
      </p>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '15px',
        marginTop: '20px'
      }}>
        {content.advantages.map((advantage, index) => (
          <div key={index} style={{
            padding: '10px 15px',
            backgroundColor: '#ffffff',
            borderRadius: '5px',
            border: '1px solid #e9ecef',
            fontSize: '14px'
          }}>
            ✓ {advantage}
          </div>
        ))}
      </div>
    </div>
  );
}

// Компонент для локальных структурированных данных
export function LocalBusinessStructuredData({ city }) {
  const cityData = KAZAKHSTAN_CITIES.find(c => c.name === city);
  
  if (!cityData) return <LocalBusinessSchema />;

  const schema = {
    "@context": "https://schema.org",
    "@type": "Store",
    "name": `SADI Shop ${city}`,
    "description": `Интернет-магазин строительных материалов в ${city}, ${cityData.region}`,
    "url": "https://shop.sadi.kz",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "KZ",
      "addressRegion": cityData.region,
      "addressLocality": city
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": cityData.coordinates.lat.toString(),
      "longitude": cityData.coordinates.lng.toString()
    },
    "areaServed": {
      "@type": "City",
      "name": city,
      "containedInPlace": {
        "@type": "State",
        "name": cityData.region
      }
    },
    "openingHours": "Mo-Su 00:00-23:59",
    "priceRange": "$$",
    "paymentAccepted": "Cash, Credit Card, Bank Transfer",
    "currenciesAccepted": "KZT"
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}

// Компонент для локальных мета-тегов
export function LocalMetaTags({ city, category }) {
  const content = generateLocalContent(city, category);
  const keywords = generateLocalKeywords(category || "строительные материалы", city);
  
  if (!content) return null;

  return (
    <>
      <meta name="geo.region" content="KZ" />
      <meta name="geo.placename" content={city} />
      <meta name="geo.position" content={`${KAZAKHSTAN_CITIES.find(c => c.name === city)?.coordinates.lat};${KAZAKHSTAN_CITIES.find(c => c.name === city)?.coordinates.lng}`} />
      <meta name="ICBM" content={`${KAZAKHSTAN_CITIES.find(c => c.name === city)?.coordinates.lat}, ${KAZAKHSTAN_CITIES.find(c => c.name === city)?.coordinates.lng}`} />
      <meta name="keywords" content={keywords} />
      
      {/* Open Graph локальные теги */}
      <meta property="og:locality" content={city} />
      <meta property="og:region" content={KAZAKHSTAN_CITIES.find(c => c.name === city)?.region} />
      <meta property="og:country-name" content="Kazakhstan" />
    </>
  );
}

// Хук для определения города пользователя
export function useUserLocation() {
  const [userCity, setUserCity] = useState(null);

  useEffect(() => {
    // Попытка определить город по IP или геолокации
    const detectCity = async () => {
      try {
        // Можно использовать сервис геолокации
        const response = await fetch('https://ipapi.co/json/');
        const data = await response.json();
        
        if (data.city && KAZAKHSTAN_CITIES.find(c => c.name === data.city)) {
          setUserCity(data.city);
        }
      } catch (error) {
        console.log('Не удалось определить город пользователя');
      }
    };

    detectCity();
  }, []);

  return userCity;
}

export default {
  KAZAKHSTAN_CITIES,
  generateLocalKeywords,
  generateLocalContent,
  LocalContent,
  LocalBusinessStructuredData,
  LocalMetaTags,
  useUserLocation
};
