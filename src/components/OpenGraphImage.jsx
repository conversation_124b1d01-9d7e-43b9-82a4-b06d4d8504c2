// Компонент для генерации Open Graph изображений

import { ImageResponse } from 'next/og';

// Базовые стили для OG изображений
const baseStyles = {
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#ffffff',
  backgroundImage: 'linear-gradient(45deg, #f8f9fa 25%, transparent 25%), linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f8f9fa 75%), linear-gradient(-45deg, transparent 75%, #f8f9fa 75%)',
  backgroundSize: '20px 20px',
  backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
};

// Генерация OG изображения для товара
export async function generateProductOGImage(product) {
  return new ImageResponse(
    (
      <div style={baseStyles}>
        {/* Логотип */}
        <div style={{
          position: 'absolute',
          top: '40px',
          left: '40px',
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#0066cc',
        }}>
          SADI Shop
        </div>

        {/* Основной контент */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          maxWidth: '800px',
          padding: '0 40px',
        }}>
          {/* Название товара */}
          <h1 style={{
            fontSize: '48px',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '20px',
            lineHeight: '1.2',
          }}>
            {product.Name}
          </h1>

          {/* Цена */}
          {product.RetailPrice && (
            <div style={{
              fontSize: '36px',
              fontWeight: 'bold',
              color: '#0066cc',
              marginBottom: '20px',
            }}>
              {product.RetailPrice.toLocaleString()} ₸
            </div>
          )}

          {/* Описание */}
          <p style={{
            fontSize: '24px',
            color: '#666',
            marginBottom: '40px',
            maxWidth: '600px',
          }}>
            Строительный материал в интернет-магазине SADI Shop
          </p>
        </div>

        {/* Нижний блок */}
        <div style={{
          position: 'absolute',
          bottom: '40px',
          right: '40px',
          fontSize: '18px',
          color: '#999',
        }}>
          shop.sadi.kz
        </div>
      </div>
    ),
    {
      width: 1200,
      height: 630,
    }
  );
}

// Генерация OG изображения для категории
export async function generateCategoryOGImage(categoryName, productsCount) {
  return new ImageResponse(
    (
      <div style={baseStyles}>
        {/* Логотип */}
        <div style={{
          position: 'absolute',
          top: '40px',
          left: '40px',
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#0066cc',
        }}>
          SADI Shop
        </div>

        {/* Основной контент */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          maxWidth: '800px',
          padding: '0 40px',
        }}>
          {/* Заголовок */}
          <h1 style={{
            fontSize: '48px',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '20px',
            lineHeight: '1.2',
          }}>
            {categoryName}
          </h1>

          {/* Количество товаров */}
          <div style={{
            fontSize: '32px',
            color: '#0066cc',
            marginBottom: '20px',
          }}>
            {productsCount ? `${productsCount.toLocaleString()} товаров` : 'Каталог товаров'}
          </div>

          {/* Описание */}
          <p style={{
            fontSize: '24px',
            color: '#666',
            marginBottom: '40px',
            maxWidth: '600px',
          }}>
            Строительные материалы с доставкой по Казахстану
          </p>
        </div>

        {/* Нижний блок */}
        <div style={{
          position: 'absolute',
          bottom: '40px',
          right: '40px',
          fontSize: '18px',
          color: '#999',
        }}>
          shop.sadi.kz
        </div>
      </div>
    ),
    {
      width: 1200,
      height: 630,
    }
  );
}

// Генерация OG изображения для поиска
export async function generateSearchOGImage(searchQuery, resultsCount) {
  return new ImageResponse(
    (
      <div style={baseStyles}>
        {/* Логотип */}
        <div style={{
          position: 'absolute',
          top: '40px',
          left: '40px',
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#0066cc',
        }}>
          SADI Shop
        </div>

        {/* Основной контент */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          maxWidth: '800px',
          padding: '0 40px',
        }}>
          {/* Поисковый запрос */}
          <h1 style={{
            fontSize: '48px',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '20px',
            lineHeight: '1.2',
          }}>
            "{searchQuery}"
          </h1>

          {/* Результаты поиска */}
          <div style={{
            fontSize: '32px',
            color: '#0066cc',
            marginBottom: '20px',
          }}>
            {resultsCount ? `Найдено ${resultsCount.toLocaleString()} товаров` : 'Результаты поиска'}
          </div>

          {/* Описание */}
          <p style={{
            fontSize: '24px',
            color: '#666',
            marginBottom: '40px',
            maxWidth: '600px',
          }}>
            Поиск строительных материалов в SADI Shop
          </p>
        </div>

        {/* Нижний блок */}
        <div style={{
          position: 'absolute',
          bottom: '40px',
          right: '40px',
          fontSize: '18px',
          color: '#999',
        }}>
          shop.sadi.kz
        </div>
      </div>
    ),
    {
      width: 1200,
      height: 630,
    }
  );
}

// Генерация базового OG изображения
export async function generateDefaultOGImage(title, description) {
  return new ImageResponse(
    (
      <div style={baseStyles}>
        {/* Логотип */}
        <div style={{
          position: 'absolute',
          top: '40px',
          left: '40px',
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#0066cc',
        }}>
          SADI Shop
        </div>

        {/* Основной контент */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          maxWidth: '800px',
          padding: '0 40px',
        }}>
          {/* Заголовок */}
          <h1 style={{
            fontSize: '48px',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '20px',
            lineHeight: '1.2',
          }}>
            {title}
          </h1>

          {/* Описание */}
          <p style={{
            fontSize: '24px',
            color: '#666',
            marginBottom: '40px',
            maxWidth: '600px',
          }}>
            {description}
          </p>
        </div>

        {/* Нижний блок */}
        <div style={{
          position: 'absolute',
          bottom: '40px',
          right: '40px',
          fontSize: '18px',
          color: '#999',
        }}>
          shop.sadi.kz
        </div>
      </div>
    ),
    {
      width: 1200,
      height: 630,
    }
  );
}
