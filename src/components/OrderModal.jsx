"use client";

import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { useAuth } from "../context/AuthContext";

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
`;
ModalOverlay.displayName = "ModalOverlay";

const ModalContent = styled.div`
  background: white;
  border-radius: 16px;
  padding: 32px;
  width: 100%;
  max-width: 500px;
  position: relative;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  @media (max-width: 768px) {
    padding: 24px;
    margin: 16px;
    max-width: calc(100% - 32px);
  }
`;
ModalContent.displayName = "ModalContent";

const CloseButton = styled.button`
  position: absolute;
  top: 24px;
  right: 24px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #666;

  &:hover {
    color: #333;
  }

  svg {
    width: 24px;
    height: 24px;
  }
`;
CloseButton.displayName = "CloseButton";

const Title = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 32px 0;

  @media (max-width: 768px) {
    font-size: 20px;
    margin-bottom: 24px;
  }
`;
Title.displayName = "Title";

const FormGroup = styled.div`
  margin-bottom: 24px;
`;
FormGroup.displayName = "FormGroup";

const Label = styled.label`
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
`;
Label.displayName = "Label";

const Input = styled.input`
  width: 100%;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
  background-color: #f8f9fa;
  transition: border-color 0.2s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: #0066cc;
    background-color: white;
  }

  &:disabled {
    background-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    border-color: #ced4da;
  }

  &::placeholder {
    color: #999;
  }
`;
Input.displayName = "Input";

const SubmitButton = styled.button`
  width: 100%;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 8px;

  &:hover {
    background-color: #0055b3;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;
SubmitButton.displayName = "SubmitButton";

// Стили для уведомлений
const SuccessNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #28a745;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
SuccessNotification.displayName = "SuccessNotification";

const SuccessNotificationText = styled.span`
  flex: 1;
`;
SuccessNotificationText.displayName = "SuccessNotificationText";

const SuccessNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
SuccessNotificationClose.displayName = "SuccessNotificationClose";

const ErrorNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
ErrorNotification.displayName = "ErrorNotification";

const OrderModal = ({ isOpen, onClose, cartItems, getItemPrices }) => {
  const { user, isAuthenticated } = useAuth();
  const [formData, setFormData] = useState({
    companyName: "",
    email: "",
    phone: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Состояния для уведомлений
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");

  // Автоматически заполняем форму данными авторизованного пользователя
  useEffect(() => {
    if (isAuthenticated && user) {
      setFormData((prev) => ({
        ...prev,
        email: user.email || prev.email,
        companyName: user.companyName || prev.companyName,
        phone: user.phone || prev.phone,
      }));
    }
  }, [isAuthenticated, user]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const generateOrderHTML = () => {
    const itemsHTML = cartItems
      .map((item) => {
        const prices = getItemPrices(item);
        const quantity =
          typeof item.quantity === "number"
            ? item.quantity
            : parseInt(item.quantity, 10) || 0;
        const totalPrice = prices.retailPrice
          ? prices.retailPrice * quantity
          : 0;

        return `
        <tr>
          <td><strong>Наименование:</strong></td>
          <td style="padding-left: 10px;">${item.title}</td>
        </tr>
        <tr><td colspan="2" style="height:10px;"></td></tr>
        <tr>
          <td><strong>Количество:</strong></td>
          <td style="padding-left: 10px;">${quantity}</td>
        </tr>
        <tr><td colspan="2" style="height:10px;"></td></tr>
        <tr>
          <td><strong>Цена за единицу:</strong></td>
          <td style="padding-left: 10px;">${
            prices.retailPrice
              ? `${prices.retailPrice.toLocaleString()} ₸`
              : "По запросу"
          }</td>
        </tr>
        <tr><td colspan="2" style="height:10px;"></td></tr>
        <tr>
          <td><strong>Итоговая цена:</strong></td>
          <td style="padding-left: 10px;">${
            totalPrice > 0 ? `${totalPrice.toLocaleString()} ₸` : "По запросу"
          }</td>
        </tr>
        <tr><td colspan="2" style="height:20px;"></td></tr>
      `;
      })
      .join("");

    return `<div style="font-family: Arial, sans-serif; background: #ffffff; padding: 30px; text-align: center; color: #000;">
  <h2 style="color:#00a0df; margin-top:0;">SADI.KZ</h2>
  <p style="font-size:18px; margin: 10px 0 30px;">Вам пришла заявка клиента от <a href="https://sadi.kz" style="color:#00a0df; text-decoration: none;">sadi.kz</a></p>
  <h3 style="margin: 20px 0;">Заявка</h3>
  <table style="margin: 0 auto; text-align: left; font-size: 16px;">
    <tr>
      <td><strong>Имя компании:</strong></td>
      <td style="padding-left: 10px;">${formData.companyName}</td>
    </tr>
    <tr><td colspan="2" style="height:10px;"></td></tr>
    <tr>
      <td><strong>Телефон:</strong></td>
      <td style="padding-left: 10px;">${formData.phone}</td>
    </tr>
    <tr><td colspan="2" style="height:10px;"></td></tr>
    <tr>
      <td><strong>E-mail:</strong></td>
      <td style="padding-left: 10px;"><a href="mailto:${formData.email}" style="color:#0000ee;">${formData.email}</a></td>
    </tr>
    <tr><td colspan="2" style="height:20px;"></td></tr>
    ${itemsHTML}
  </table>
  <hr style="margin: 30px 0; border: none; border-top: 1px solid #ccc;" />
  <p style="font-size:14px; color:#555;">Единая база поставщиков строительных материалов и услуг</p>
  <p style="font-size:14px; color:#555;">
    Тел: <a href="tel:+77086083103" style="color:#6a1b9a; text-decoration: none;">+7 (708) 608 31 03</a>
  </p>
</div>`;
  };

  // Функции для управления уведомлениями
  const showSuccess = (message) => {
    setNotificationMessage(message);
    setShowSuccessNotification(true);
    setTimeout(() => {
      setShowSuccessNotification(false);
    }, 3000);
  };

  const showError = (message) => {
    setNotificationMessage(message);
    setShowErrorNotification(true);
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 3000);
  };

  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
  };

  const handleCloseErrorNotification = () => {
    setShowErrorNotification(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const orderData = {
        Email: formData.email,
        Title: formData.companyName,
        Body: generateOrderHTML(),
      };

      const response = await fetch("https://api.sadi.kz/api/Mail/Send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(orderData),
      });

      if (response.ok) {
        showSuccess("Заказ успешно отправлен!");
        setFormData({ companyName: "", email: "", phone: "" });
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        throw new Error("Ошибка при отправке заказа");
      }
    } catch (error) {
      console.error("Ошибка:", error);
      showError("Произошла ошибка при отправке заказа. Попробуйте еще раз.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Уведомления */}
      {showSuccessNotification && (
        <SuccessNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseSuccessNotification}>
            ×
          </SuccessNotificationClose>
        </SuccessNotification>
      )}

      {showErrorNotification && (
        <ErrorNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseErrorNotification}>
            ×
          </SuccessNotificationClose>
        </ErrorNotification>
      )}

      <ModalOverlay onClick={handleOverlayClick}>
        <ModalContent>
          <CloseButton onClick={onClose}>
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </CloseButton>

          <Title>Оформление заказа</Title>

          <form onSubmit={handleSubmit}>
            <FormGroup>
              <Label htmlFor="companyName">Имя компании</Label>
              <Input
                id="companyName"
                name="companyName"
                type="text"
                placeholder="Введите название компании"
                value={formData.companyName}
                onChange={handleInputChange}
                disabled={isAuthenticated && user?.companyName}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="email">Почта</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleInputChange}
                disabled={isAuthenticated}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="phone">Телефон</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                placeholder="+7 (___) ___-__-__"
                value={formData.phone}
                onChange={handleInputChange}
                disabled={isAuthenticated && user?.phone}
              />
            </FormGroup>

            <SubmitButton type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Отправка..." : "Отправить заказ"}
            </SubmitButton>
          </form>
        </ModalContent>
      </ModalOverlay>
    </>
  );
};

export default OrderModal;
