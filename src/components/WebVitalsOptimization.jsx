"use client";

import { useEffect } from "react";
import { useReportWebVitals } from "next/web-vitals";

// Компонент для оптимизации Core Web Vitals
export default function WebVitalsOptimization() {
  // Отчет о Web Vitals
  useReportWebVitals((metric) => {
    // Отправляем метрики в Google Analytics
    if (typeof window !== "undefined" && window.gtag) {
      window.gtag("event", metric.name, {
        event_category: "Web Vitals",
        value: Math.round(metric.value),
        event_label: metric.id,
        non_interaction: true,
      });
    }

    // Отправляем метрики в Яндекс.Метрику
    if (typeof window !== "undefined" && window.ym) {
      window.ym(51364636, "reachGoal", `web_vitals_${metric.name.toLowerCase()}`, {
        value: Math.round(metric.value),
        label: metric.id,
      });
    }

    // Логируем в консоль для разработки
    if (process.env.NODE_ENV === "development") {
      console.log(`Web Vital ${metric.name}:`, metric);
    }
  });

  useEffect(() => {
    // Оптимизация изображений - добавляем loading="lazy" ко всем изображениям
    const optimizeImages = () => {
      const images = document.querySelectorAll("img:not([loading])");
      images.forEach((img) => {
        img.setAttribute("loading", "lazy");
        img.setAttribute("decoding", "async");
      });
    };

    // Оптимизация шрифтов
    const optimizeFonts = () => {
      // Добавляем font-display: swap для всех шрифтов
      const style = document.createElement("style");
      style.textContent = `
        @font-face {
          font-display: swap;
        }
      `;
      document.head.appendChild(style);
    };

    // Предзагрузка критических ресурсов
    const preloadCriticalResources = () => {
      // Предзагружаем API для каталога
      const link = document.createElement("link");
      link.rel = "prefetch";
      link.href = "/api/Materials";
      document.head.appendChild(link);

      // Предзагружаем следующую страницу каталога
      const currentPage = window.location.pathname.match(/\/page\/(\d+)/);
      if (currentPage) {
        const nextPage = parseInt(currentPage[1]) + 1;
        const nextPageLink = document.createElement("link");
        nextPageLink.rel = "prefetch";
        nextPageLink.href = `/products/page/${nextPage}`;
        document.head.appendChild(nextPageLink);
      }
    };

    // Оптимизация скролла
    const optimizeScrolling = () => {
      // Добавляем smooth scrolling
      document.documentElement.style.scrollBehavior = "smooth";

      // Оптимизируем обработчики скролла
      let ticking = false;
      const handleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            // Здесь можно добавить логику для lazy loading
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener("scroll", handleScroll, { passive: true });
      return () => window.removeEventListener("scroll", handleScroll);
    };

    // Оптимизация для мобильных устройств
    const optimizeForMobile = () => {
      // Добавляем viewport meta tag если его нет
      if (!document.querySelector('meta[name="viewport"]')) {
        const viewport = document.createElement("meta");
        viewport.name = "viewport";
        viewport.content = "width=device-width, initial-scale=1, viewport-fit=cover";
        document.head.appendChild(viewport);
      }

      // Оптимизируем touch события
      document.addEventListener("touchstart", () => {}, { passive: true });
    };

    // Запускаем оптимизации
    optimizeImages();
    optimizeFonts();
    preloadCriticalResources();
    const cleanupScroll = optimizeScrolling();
    optimizeForMobile();

    // Cleanup
    return () => {
      if (cleanupScroll) cleanupScroll();
    };
  }, []);

  return null;
}

// Компонент для критических CSS стилей
export function CriticalCSS() {
  return (
    <style jsx global>{`
      /* Критические стили для улучшения LCP */
      html {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        line-height: 1.6;
      }

      body {
        margin: 0;
        padding: 0;
        background-color: #ffffff;
        color: #333;
      }

      /* Оптимизация для CLS */
      img {
        max-width: 100%;
        height: auto;
        display: block;
      }

      /* Skeleton для карточек товаров */
      .product-card-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% {
          background-position: 200% 0;
        }
        100% {
          background-position: -200% 0;
        }
      }

      /* Оптимизация для FID */
      button, a, input, select, textarea {
        touch-action: manipulation;
      }

      /* Предотвращение layout shift */
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }

      .header {
        min-height: 80px;
        background-color: #0066cc;
      }

      .main-content {
        min-height: 400px;
      }

      /* Оптимизация шрифтов */
      @font-face {
        font-family: "System";
        src: local("-apple-system"), local("BlinkMacSystemFont"), local("Segoe UI"), local("Roboto");
        font-display: swap;
      }
    `}</style>
  );
}

// Компонент для Resource Hints
export function ResourceHints() {
  return (
    <>
      {/* DNS prefetch для внешних доменов */}
      <link rel="dns-prefetch" href="//api.sadi.kz" />
      <link rel="dns-prefetch" href="//test.api.sadi.kz" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      <link rel="dns-prefetch" href="//mc.yandex.ru" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />

      {/* Preconnect для критических ресурсов */}
      <link rel="preconnect" href="https://api.sadi.kz" />
      <link rel="preconnect" href="https://fonts.googleapis.com" crossOrigin="" />

      {/* Preload для критических ресурсов */}
      <link rel="preload" href="/images/placeholder.png" as="image" />
      
      {/* Module preload для критических JS */}
      <link rel="modulepreload" href="/_next/static/chunks/main.js" />
    </>
  );
}

// Хук для отслеживания производительности
export function usePerformanceMonitoring() {
  useEffect(() => {
    // Отслеживание времени загрузки страницы
    const measurePageLoad = () => {
      if (typeof window !== "undefined" && window.performance) {
        const navigation = performance.getEntriesByType("navigation")[0];
        
        if (navigation && window.gtag) {
          window.gtag("event", "page_load_time", {
            event_category: "Performance",
            value: Math.round(navigation.loadEventEnd - navigation.fetchStart),
            non_interaction: true,
          });
        }
      }
    };

    // Отслеживание ошибок JavaScript
    const handleError = (event) => {
      if (window.gtag) {
        window.gtag("event", "javascript_error", {
          event_category: "Error",
          event_label: event.error?.message || "Unknown error",
          non_interaction: true,
        });
      }
    };

    // Отслеживание отклоненных промисов
    const handleUnhandledRejection = (event) => {
      if (window.gtag) {
        window.gtag("event", "unhandled_promise_rejection", {
          event_category: "Error",
          event_label: event.reason?.message || "Unknown rejection",
          non_interaction: true,
        });
      }
    };

    window.addEventListener("load", measurePageLoad);
    window.addEventListener("error", handleError);
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    return () => {
      window.removeEventListener("load", measurePageLoad);
      window.removeEventListener("error", handleError);
      window.removeEventListener("unhandledrejection", handleUnhandledRejection);
    };
  }, []);
}
