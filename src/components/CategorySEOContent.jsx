"use client";

import React from "react";
import styled from "styled-components";

const SEOContainer = styled.div`
  margin-top: 48px;
  padding: 32px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;

  @media (max-width: 768px) {
    margin-top: 32px;
    padding: 24px 16px;
  }
`;
SEOContainer.displayName = "SEOContainer";

const SEOTitle = styled.h1`
  font-size: 32px;
  font-weight: 700;
  color: #333;
  margin-bottom: 24px;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 28px;
    margin-bottom: 20px;
  }
`;
SEOTitle.displayName = "SEOTitle";

const SEOSubtitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 32px 0 16px 0;

  @media (max-width: 768px) {
    font-size: 20px;
    margin: 24px 0 12px 0;
  }
`;
SEOSubtitle.displayName = "SEOSubtitle";

const SEOText = styled.div`
  font-size: 16px;
  line-height: 1.6;
  color: #555;

  p {
    margin-bottom: 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 24px 0 16px 0;
  }

  ul,
  ol {
    margin: 16px 0;
    padding-left: 24px;
  }

  li {
    margin-bottom: 8px;
  }

  strong {
    color: #0066cc;
    font-weight: 600;
  }

  @media (max-width: 768px) {
    font-size: 14px;

    h3 {
      font-size: 18px;
      margin: 20px 0 12px 0;
    }

    ul,
    ol {
      padding-left: 20px;
    }
  }
`;
SEOText.displayName = "SEOText";

const FAQSection = styled.div`
  margin-top: 32px;
  padding: 24px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
`;
FAQSection.displayName = "FAQSection";

const FAQItem = styled.div`
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
`;
FAQItem.displayName = "FAQItem";

const FAQQuestion = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
`;
FAQQuestion.displayName = "FAQQuestion";

const FAQAnswer = styled.p`
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0;
`;
FAQAnswer.displayName = "FAQAnswer";

// Генерация SEO-контента на основе категории
const generateCategoryContent = (categoryInfo) => {
  const categoryName = categoryInfo.name.toLowerCase();

  // Определяем тип материала для персонализированного контента
  let materialType = "строительные материалы";
  let specificContent = "";

  if (categoryName.includes("бетон")) {
    materialType = "бетон и бетонные смеси";
    specificContent = `
      Бетон - это основа современного строительства. В нашем каталоге представлены различные марки и классы бетона:
      от легких бетонов для теплоизоляции до высокопрочных составов для фундаментов и несущих конструкций.
    `;
  } else if (categoryName.includes("щебень")) {
    materialType = "щебень различных фракций";
    specificContent = `
      Щебень - незаменимый материал для дорожного строительства и производства бетона.
      Мы предлагаем гранитный, известняковый и гравийный щебень различных фракций от 5-10 мм до 40-70 мм.
    `;
  } else if (categoryName.includes("асфальт")) {
    materialType = "асфальт и асфальтобетонные смеси";
    specificContent = `
      Асфальт и асфальтобетонные смеси для дорожного строительства.
      Высококачественные материалы для устройства дорожных покрытий, тротуаров и площадок.
    `;
  } else if (categoryName.includes("дорог")) {
    materialType = "материалы для дорожного строительства";
    specificContent = `
      Полный комплекс материалов для дорожного строительства: от основания до финишного покрытия.
      Все материалы соответствуют государственным стандартам и техническим требованиям.
    `;
  }

  return { materialType, specificContent };
};

const CategorySEOContent = ({ categoryInfo }) => {
  if (!categoryInfo) return null;

  const { materialType, specificContent } =
    generateCategoryContent(categoryInfo);
  const categoryName = categoryInfo.name.toLowerCase();

  return (
    <SEOContainer>
      <SEOTitle>Купить {categoryName} в Казахстане</SEOTitle>

      <SEOText>
        <p>
          <strong>{categoryInfo.name}</strong> -{" "}
          {specificContent ||
            `высококачественные ${materialType}, которые вы можете купить в нашем маркетплейсе по выгодным ценам.`}
        </p>

        <SEOSubtitle>
          Преимущества покупки {categoryName} в нашем каталоге:
        </SEOSubtitle>
        <ul>
          <li> Широкий ассортимент {materialType}</li>
          <li> Лучшие цены от проверенных поставщиков</li>
          <li> Быстрая доставка по всему Казахстану</li>
          <li> Гарантия качества всех материалов</li>
          <li> Возможность покупки оптом и в розницу</li>
          <li> Профессиональная консультация специалистов</li>
          <li> Сертификаты качества и паспорта на материалы</li>
        </ul>

        <h3>Как заказать {categoryName}:</h3>
        <ol>
          <li>Выберите подходящий материал из каталога</li>
          <li>Сравните цены от разных поставщиков</li>
          <li>Добавьте товар в корзину</li>
          <li>Оформите заказ с указанием адреса доставки</li>
          <li>Получите {categoryName} в удобное для вас время</li>
        </ol>

        <p>
          Наш маркетплейс объединяет ведущих поставщиков {materialType} в
          Казахстане. Мы гарантируем прозрачность цен, качество материалов и
          надежность поставок. Заказывая {categoryName} у нас, вы получаете
          доступ к лучшим предложениям рынка.
        </p>
      </SEOText>

      <FAQSection>
        <SEOSubtitle>Часто задаваемые вопросы</SEOSubtitle>

        <FAQItem>
          <FAQQuestion>
            Какие виды {categoryName} представлены в каталоге?
          </FAQQuestion>
          <FAQAnswer>
            В нашем каталоге представлен широкий ассортимент {materialType} от
            ведущих производителей. Все материалы соответствуют государственным
            стандартам качества.
          </FAQAnswer>
        </FAQItem>

        <FAQItem>
          <FAQQuestion>
            Как быстро осуществляется доставка {categoryName}?
          </FAQQuestion>
          <FAQAnswer>
            Сроки доставки зависят от региона и объема заказа. Обычно доставка
            осуществляется в течение 1-3 рабочих дней. Точные сроки уточняйте у
            поставщика при оформлении заказа.
          </FAQAnswer>
        </FAQItem>

        <FAQItem>
          <FAQQuestion>Можно ли купить {categoryName} оптом?</FAQQuestion>
          <FAQAnswer>
            Да, большинство поставщиков предлагают оптовые цены при заказе
            больших объемов. Для получения оптовых цен свяжитесь с поставщиком
            напрямую.
          </FAQAnswer>
        </FAQItem>

        <FAQItem>
          <FAQQuestion>Предоставляются ли сертификаты качества?</FAQQuestion>
          <FAQAnswer>
            Все материалы поставляются с необходимыми сертификатами качества и
            паспортами. Документы предоставляются поставщиком при отгрузке
            товара.
          </FAQAnswer>
        </FAQItem>
      </FAQSection>
    </SEOContainer>
  );
};

CategorySEOContent.displayName = "CategorySEOContent";

export default CategorySEOContent;
